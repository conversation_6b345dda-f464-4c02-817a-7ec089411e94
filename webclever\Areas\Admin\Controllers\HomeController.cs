using Microsoft.AspNetCore.Mvc;
using webclever.Models;
using Microsoft.EntityFrameworkCore;

namespace webclever.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class HomeController : BaseController
    {
        private readonly WebcleverContext _context;

        public HomeController(WebcleverContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            // Lấy thông tin user từ session (nếu có, hoặc giữ giá trị giả định từ BaseController)
            ViewBag.UserId = HttpContext.Session.GetString("UserId") ?? "AdminTest";
            ViewBag.UserName = HttpContext.Session.GetString("UserName") ?? "Test Admin";
            ViewBag.UserEmail = HttpContext.Session.GetString("UserEmail") ?? "<EMAIL>";
            ViewBag.UserRole = HttpContext.Session.GetString("UserRole") ?? "Admin";

            // L<PERSON>y dữ liệu thống kê
            ViewBag.TotalOrders = await _context.Orders.CountAsync();
            ViewBag.TotalRevenue = await _context.Orders.SumAsync(o => o.TotalAmount ?? 0);
            ViewBag.TotalProducts = await _context.Products.CountAsync();
            ViewBag.TotalUsers = await _context.Users.CountAsync();

            // Đơn hàng gần đây
            ViewBag.RecentOrders = await _context.Orders
                .Include(o => o.User)
                .OrderByDescending(o => o.OrderDate)
                .Take(5)
                .ToListAsync();

            // Sản phẩm sắp hết hàng
            ViewBag.LowStockProducts = await _context.ProductVariants
                .Include(pv => pv.Product)
                .Where(pv => pv.Stock <= 10 && pv.Stock > 0)
                .OrderBy(pv => pv.Stock)
                .Take(5)
                .ToListAsync();

            return View();
        }
    }
} 