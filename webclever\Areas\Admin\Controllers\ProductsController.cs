using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webclever.Models;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Drawing;
using System.Drawing.Imaging;

namespace webclever.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class ProductsController : Controller
    {
        private readonly WebcleverContext _context;
        private readonly IWebHostEnvironment _environment;

        public ProductsController(WebcleverContext context, IWebHostEnvironment environment)
        {
            _context = context;
            _environment = environment;
            
            // Tạo thư mục images nếu chưa tồn tại
            string imagesFolder = Path.Combine(_environment.WebRootPath, "images");
            if (!Directory.Exists(imagesFolder))
            {
                Directory.CreateDirectory(imagesFolder);
            }
            
            // Kiểm tra và tạo file no-image.png nếu chưa tồn tại
            string noImagePath = Path.Combine(imagesFolder, "no-image.png");
            if (!System.IO.File.Exists(noImagePath))
            {
                if (OperatingSystem.IsWindows())
                {
                    // Tạo một hình ảnh trống đơn giản
                    using (var image = new System.Drawing.Bitmap(200, 200))
                    {
                        using (var graphics = System.Drawing.Graphics.FromImage(image))
                        {
                            graphics.Clear(System.Drawing.Color.LightGray);
                            using (var font = new System.Drawing.Font("Arial", 20))
                            {
                                var text = "No Image";
                                var textSize = graphics.MeasureString(text, font);
                                var x = (image.Width - textSize.Width) / 2;
                                var y = (image.Height - textSize.Height) / 2;
                                graphics.DrawString(text, font, System.Drawing.Brushes.DarkGray, x, y);
                            }
                        }
                        image.Save(noImagePath, System.Drawing.Imaging.ImageFormat.Png);
                    }
                }
                else
                {
                    // Tạo file trống cho các hệ điều hành khác
                    System.IO.File.Create(noImagePath).Dispose();
                }
            }
        }

        // GET: Admin/Products
        public async Task<IActionResult> Index()
        {
            var products = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.ProductVariants)
                .ToListAsync();
            return View(products);
        }

        // GET: Admin/Products/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.ProductVariants)
                .FirstOrDefaultAsync(m => m.ProductId == id);

            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // GET: Admin/Products/Create
        public IActionResult Create()
        {
            ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "Name");
            return View();
        }

        // POST: Admin/Products/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(
        [Bind("Name,Brand,CategoryId,Description")] Product product,
        List<ProductVariant> variants,
        IFormFile? imageFile)
        {
            // ----- log request --------------------------------------------------
            var req = HttpContext.Request;
            Console.WriteLine($"» POST /Admin/Products/Create  CT={req.ContentType}, Len={req.ContentLength}");

            // --------------------------------------------------------------------
            if (!ModelState.IsValid)
            {
                ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "Name", product.CategoryId);
                return View(product);
            }

            try
            {
                /* 1. ẢNH SẢN PHẨM CHÍNH ---------------------------------------------------- */
                if (imageFile is { Length: > 0 })
                {
                    string root = Path.Combine(_environment.WebRootPath, "uploads/products");
                    Directory.CreateDirectory(root);                        // idempotent
                    string unique = $"{Guid.NewGuid()}_{imageFile.FileName}";
                    string path = Path.Combine(root, unique);

                    await using var fs = new FileStream(path, FileMode.Create);
                    await imageFile.CopyToAsync(fs);

                    product.ImageUrl = $"/uploads/products/{unique}";
                }

                //// Kiểm tra trùng sản phẩm theo tên và brand
                //bool existed = await _context.Products.AnyAsync(p =>
                //    p.Name == product.Name &&
                //    p.Brand == product.Brand &&
                //    p.CategoryId == product.CategoryId
                //);

                //if (existed)
                //{
                //    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                //    {
                //        return Json(new { success = false, message = "Sản phẩm đã tồn tại!" });
                //    }

                //    ModelState.AddModelError("", "Sản phẩm đã tồn tại.");
                //    ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "Name", product.CategoryId);
                //    return View(product);
                //}

                _context.Products.Add(product);
                await _context.SaveChangesAsync();          // cần Id cho biến thể

                /* 2. XỬ LÝ BIẾN THỂ -------------------------------------------------------- */
                if (variants is not null)
                {
                    for (int i = 0; i < variants.Count; i++)
                    {
                        var v = variants[i];
                        if (v is null) continue;

                        // nếu không có dữ liệu thực, bỏ qua
                        if (string.IsNullOrWhiteSpace(v.Color) &&
                            string.IsNullOrWhiteSpace(v.Storage) &&
                            !v.Price.HasValue && !v.Stock.HasValue)
                            continue;

                        v.ProductId = product.ProductId;

                        /* lấy đúng file theo tên: VariantImages[i] */
                        var file = req.Form.Files
                                      .FirstOrDefault(f => f.Name == $"VariantImages[{i}]");

                        if (file is { Length: > 0 })
                        {
                            try
                            {
                                string root = Path.Combine(_environment.WebRootPath, "uploads/variants");
                                Directory.CreateDirectory(root);
                                string unique = $"{Guid.NewGuid()}_{file.FileName}";
                                string path = Path.Combine(root, unique);

                                await using var fs = new FileStream(path, FileMode.Create);
                                await file.CopyToAsync(fs);

                                v.ImageUrl = $"/uploads/variants/{unique}";
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"⚠️  lỗi lưu ảnh biến thể {i}: {ex.Message}");
                                v.ImageUrl = "/images/no-image.png";
                            }
                        }
                        else
                        {
                            v.ImageUrl = "/images/no-image.png";
                        }

                        _context.ProductVariants.Add(v);
                    }

                    await _context.SaveChangesAsync();
                }

                /* 3. PHẢN HỒI -------------------------------------------------------------- */
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    return Json(new { success = true, redirectUrl = Url.Action("Index") });

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                Console.WriteLine("❌  Lỗi Create: " + ex);
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    return Json(new { success = false, message = "Lỗi: " + ex.Message });

                ModelState.AddModelError(string.Empty, "Lỗi: " + ex.Message);
                ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "Name", product.CategoryId);
                return View(product);
            }
        }

        // GET: Admin/Products/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.ProductVariants)
                .FirstOrDefaultAsync(p => p.ProductId == id);

            if (product == null)
            {
                return NotFound();
            }

            ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "Name", product.CategoryId);
            return View(product);
        }

        // POST: Admin/Products/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(
    int id,
    [FromForm] Product product,
    [FromForm(Name = "Variants")] List<ProductVariant>? variants,
    [FromForm] IFormFile? imageFile)
        {
            if (id != product.ProductId)
            {
                return NotFound();
            }

            ModelState.Remove("imageFile");

            if (!ModelState.IsValid)
            {
                foreach (var modelStateEntry in ModelState.Values)
                {
                    foreach (var error in modelStateEntry.Errors)
                    {
                        Console.WriteLine($"ModelState Error: {error.ErrorMessage}");
                    }
                }
                ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "Name", product.CategoryId);
                return View(product);
            }

            try
            {
                var existingProduct = await _context.Products
                    .Include(p => p.ProductVariants)
                    .FirstOrDefaultAsync(p => p.ProductId == id);

                if (existingProduct == null)
                {
                    return NotFound();
                }

                existingProduct.Name = product.Name;
                existingProduct.Brand = product.Brand;
                existingProduct.CategoryId = product.CategoryId;
                existingProduct.Description = product.Description;

                if (imageFile != null && imageFile.Length > 0)
                {
                    try
                    {
                        string uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads/products");
                        Directory.CreateDirectory(uploadsFolder);

                        string uniqueFileName = Guid.NewGuid() + "_" + imageFile.FileName;
                        string filePath = Path.Combine(uploadsFolder, uniqueFileName);

                        using var fileStream = new FileStream(filePath, FileMode.Create);
                        await imageFile.CopyToAsync(fileStream);

                        if (!string.IsNullOrEmpty(existingProduct.ImageUrl))
                        {
                            string oldFilePath = Path.Combine(_environment.WebRootPath, existingProduct.ImageUrl.TrimStart('/'));
                            if (System.IO.File.Exists(oldFilePath))
                                System.IO.File.Delete(oldFilePath);
                        }

                        existingProduct.ImageUrl = "/uploads/products/" + uniqueFileName;
                        Console.WriteLine($"Đã lưu ảnh sản phẩm: {existingProduct.ImageUrl}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Lỗi khi xử lý ảnh sản phẩm: {ex.Message}");
                        ModelState.AddModelError("", $"Lỗi khi xử lý ảnh sản phẩm: {ex.Message}");
                    }
                }

                if (variants != null)
                {
                    var existingVariantIds = variants.Where(v => v.VariantId > 0).Select(v => v.VariantId).ToList();
                    var variantsToDelete = existingProduct.ProductVariants.Where(v => !existingVariantIds.Contains(v.VariantId)).ToList();
                    _context.ProductVariants.RemoveRange(variantsToDelete);

                    for (int i = 0; i < variants.Count; i++)
                    {
                        var variant = variants[i];
                        if (variant != null &&
                            (!string.IsNullOrEmpty(variant.Color) ||
                             !string.IsNullOrEmpty(variant.Storage) ||
                             variant.Price.HasValue ||
                             variant.Stock.HasValue))
                        {
                            var file = Request.Form.Files.FirstOrDefault(f => f.Name == $"VariantImages[{i}]");

                            if (variant.VariantId == 0)
                            {
                                var newVariant = new ProductVariant
                                {
                                    ProductId = product.ProductId,
                                    Color = variant.Color ?? string.Empty,
                                    Storage = variant.Storage ?? string.Empty,
                                    Price = variant.Price ?? 0,
                                    Stock = variant.Stock ?? 0,
                                    ImageUrl = "/images/no-image.png"
                                };

                                if (file is { Length: > 0 })
                                {
                                    try
                                    {
                                        string uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads/variants");
                                        Directory.CreateDirectory(uploadsFolder);

                                        string uniqueFileName = Guid.NewGuid() + "_" + file.FileName;
                                        string filePath = Path.Combine(uploadsFolder, uniqueFileName);

                                        using var fs = new FileStream(filePath, FileMode.Create);
                                        await file.CopyToAsync(fs);

                                        newVariant.ImageUrl = "/uploads/variants/" + uniqueFileName;
                                        Console.WriteLine($"Đã lưu ảnh biến thể mới: {newVariant.ImageUrl}");
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"Lỗi khi xử lý ảnh biến thể mới: {ex.Message}");
                                    }
                                }

                                _context.ProductVariants.Add(newVariant);
                            }
                            else
                            {
                                var oldVariant = existingProduct.ProductVariants.FirstOrDefault(v => v.VariantId == variant.VariantId);
                                if (oldVariant != null)
                                {
                                    oldVariant.Color = variant.Color ?? oldVariant.Color;
                                    oldVariant.Storage = variant.Storage ?? oldVariant.Storage;
                                    oldVariant.Price = variant.Price ?? oldVariant.Price;
                                    oldVariant.Stock = variant.Stock ?? oldVariant.Stock;

                                    if (file is { Length: > 0 })
                                    {
                                        try
                                        {
                                            string uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads/variants");
                                            Directory.CreateDirectory(uploadsFolder);

                                            string uniqueFileName = Guid.NewGuid() + "_" + file.FileName;
                                            string filePath = Path.Combine(uploadsFolder, uniqueFileName);

                                            using var fs = new FileStream(filePath, FileMode.Create);
                                            await file.CopyToAsync(fs);

                                            if (!string.IsNullOrEmpty(oldVariant.ImageUrl) && !oldVariant.ImageUrl.Equals("/images/no-image.png"))
                                            {
                                                string oldPath = Path.Combine(_environment.WebRootPath, oldVariant.ImageUrl.TrimStart('/'));
                                                if (System.IO.File.Exists(oldPath))
                                                    System.IO.File.Delete(oldPath);
                                            }

                                            oldVariant.ImageUrl = "/uploads/variants/" + uniqueFileName;
                                            Console.WriteLine($"Đã cập nhật ảnh biến thể: {oldVariant.ImageUrl}");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"Lỗi khi xử lý ảnh biến thể cũ: {ex.Message}");
                                        }
                                    }

                                    _context.ProductVariants.Update(oldVariant);
                                }
                            }
                        }
                    }
                }

                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"Lỗi khi cập nhật: {ex.Message}");
                ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "Name", product.CategoryId);
                return View(product);
            }
        }

        // GET: Admin/Products/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(m => m.ProductId == id);

            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // POST: Admin/Products/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var product = await _context.Products
                .Include(p => p.ProductVariants)
                .FirstOrDefaultAsync(p => p.ProductId == id);

            if (product != null)
            {
                // Xóa ảnh sản phẩm
                if (!string.IsNullOrEmpty(product.ImageUrl))
                {
                    string filePath = Path.Combine(_environment.WebRootPath, product.ImageUrl.TrimStart('/'));
                    if (System.IO.File.Exists(filePath))
                    {
                        System.IO.File.Delete(filePath);
                    }
                }

                // Xóa các biến thể
                _context.ProductVariants.RemoveRange(product.ProductVariants);
                _context.Products.Remove(product);
                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }

        private bool ProductExists(int id)
        {
            return _context.Products.Any(e => e.ProductId == id);
        }
    }
} 