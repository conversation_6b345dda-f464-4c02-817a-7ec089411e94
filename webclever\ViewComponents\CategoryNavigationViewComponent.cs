using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using webclever.Models;

namespace webclever.ViewComponents
{
    public class CategoryNavigationViewComponent : ViewComponent
    {
        private readonly WebcleverContext _context;

        public CategoryNavigationViewComponent(WebcleverContext context)
        {
            _context = context;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var categories = await _context.Categories.ToListAsync();
            return View(categories);
        }
    }
} 