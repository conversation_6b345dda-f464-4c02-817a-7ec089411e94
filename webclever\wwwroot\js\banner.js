﻿document.addEventListener("DOMContentLoaded", function () {
    // Initialize all banner containers
    const bannerContainers = document.querySelectorAll(".banner-container");

    bannerContainers.forEach((container, containerIndex) => {
        const slides = container.querySelectorAll(".slide");
        const dots = container.querySelectorAll(".dot");
        const prevBtn = container.querySelector(".arrow-btn.prev");
        const nextBtn = container.querySelector(".arrow-btn.next");

        let currentIndex = 0;
        const slideInterval = 5000;
        let slideTimer = null;

        function showSlide(index, direction = 'next') {
            if (!slides.length) return;

            const prevIndex = currentIndex;

            // Remove all classes first
            slides.forEach((slide, i) => {
                slide.classList.remove("active", "prev", "next");
            });

            // Set the new active slide
            slides[index].classList.add("active");

            // Add direction classes for animation
            if (prevIndex !== index) {
                if (direction === 'next') {
                    if (slides[prevIndex]) slides[prevIndex].classList.add("prev");
                } else {
                    if (slides[prevIndex]) slides[prevIndex].classList.add("next");
                }
            }

            // Update dots
            dots.forEach((dot, i) => {
                dot.classList.toggle("active", i === index);
            });

            currentIndex = index;
        }

        function nextSlide() {
            const nextIndex = (currentIndex + 1) % slides.length;
            showSlide(nextIndex, 'next');
        }

        function prevSlide() {
            const prevIndex = (currentIndex - 1 + slides.length) % slides.length;
            showSlide(prevIndex, 'prev');
        }

        function startTimer() {
            stopTimer();
            slideTimer = setInterval(nextSlide, slideInterval);
        }

        function stopTimer() {
            if (slideTimer !== null) {
                clearInterval(slideTimer);
                slideTimer = null;
            }
        }

        // Event listeners
        if (nextBtn) {
            nextBtn.addEventListener("click", () => {
                nextSlide();
                startTimer();
            });
        }

        if (prevBtn) {
            prevBtn.addEventListener("click", () => {
                prevSlide();
                startTimer();
            });
        }

        dots.forEach((dot, index) => {
            dot.addEventListener("click", () => {
                showSlide(index);
                startTimer();
            });
        });

        // Pause on hover
        container.addEventListener("mouseenter", stopTimer);
        container.addEventListener("mouseleave", startTimer);

        // Initialize this banner
        if (slides.length > 0) {
            showSlide(currentIndex);
            startTimer();
        }
    });
});
