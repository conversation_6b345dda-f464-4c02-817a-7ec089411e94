@model webclever.Models.Promotion

@{
    ViewData["Title"] = "Chỉnh sửa khuyến mãi";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chỉnh sửa khuyến mãi</h1>
        <a asp-area="Admin" asp-controller="Promotions" asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form asp-action="Edit" method="post">
                @Html.AntiForgeryToken()
                <input type="hidden" asp-for="PromotionId" />
                <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                <div class="form-group">
                    <label asp-for="Title" class="control-label">T<PERSON><PERSON> k<PERSON><PERSON>ến mãi</label>
                    <input asp-for="Title" class="form-control" />
                    <span asp-validation-for="Title" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <label asp-for="Description">Mô tả</label>
                    <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="StartDate" class="control-label">Ngày bắt đầu</label>
                            <input asp-for="StartDate" class="form-control" type="date" />
                            <span asp-validation-for="StartDate" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="EndDate" class="control-label">Ngày kết thúc</label>
                            <input asp-for="EndDate" class="form-control" type="date" />
                            <span asp-validation-for="EndDate" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label asp-for="DiscountPercent" class="control-label">Phần trăm giảm (%)</label>
                    <input asp-for="DiscountPercent" class="form-control" type="number" min="0" max="100" />
                    <span asp-validation-for="DiscountPercent" class="text-danger"></span>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Lưu thay đổi
                </button>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        // Đảm bảo ngày kết thúc không thể trước ngày bắt đầu
        $(document).ready(function () {
            $('#StartDate').change(function () {
                $('#EndDate').attr('min', $(this).val());
            });
        });
    </script>
} 