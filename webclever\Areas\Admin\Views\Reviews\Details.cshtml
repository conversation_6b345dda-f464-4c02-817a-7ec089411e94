@model webclever.Models.Review

@{
    ViewData["Title"] = "Chi tiết đánh giá";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chi tiết đánh giá</h1>
        <div>
            <a asp-action="Edit" asp-route-id="@Model.ReviewId" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
            </a>
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin đánh giá</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">ID</dt>
                        <dd class="col-sm-9">@Model.ReviewId</dd>

                        <dt class="col-sm-3">Khách hàng</dt>
                        <dd class="col-sm-9">
                            <a asp-controller="Users" asp-action="Details" asp-route-id="@Model.UserId">
                                @Model.User?.FullName
                            </a>
                        </dd>

                        <dt class="col-sm-3">Sản phẩm</dt>
                        <dd class="col-sm-9">
                            <a asp-controller="Products" asp-action="Details" asp-route-id="@Model.Variant?.ProductId">
                                @Model.Variant?.Product?.Name
                            </a>
                            @if (Model.Variant != null)
                            {
                                <span class="badge bg-info ms-2">@Model.Variant.Color - @Model.Variant.Storage</span>
                            }
                        </dd>

                        <dt class="col-sm-3">Đánh giá</dt>
                        <dd class="col-sm-9">
                            <div class="d-flex align-items-center">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    <i class="fas fa-star @(i <= Model.Rating ? "text-warning" : "text-muted")"></i>
                                }
                                <span class="ms-2">(@Model.Rating/5)</span>
                            </div>
                        </dd>

                        <dt class="col-sm-3">Nội dung</dt>
                        <dd class="col-sm-9">@Model.Comment</dd>

                        <dt class="col-sm-3">Ngày đánh giá</dt>
                        <dd class="col-sm-9">@(Model.CreatedAt.HasValue ? string.Format("{0:dd/MM/yyyy HH:mm}", Model.CreatedAt.Value) : "N/A")</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin bổ sung</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Lưu ý</h5>
                        <ul class="mb-0">
                            <li>Đánh giá không thể bị xóa sau khi đã được đăng</li>
                            <li>Chỉ có thể chỉnh sửa nội dung đánh giá</li>
                            <li>Đánh giá sẽ được hiển thị công khai trên trang sản phẩm</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 