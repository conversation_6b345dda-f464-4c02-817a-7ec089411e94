@model IEnumerable<webclever.Models.User>

@{
    ViewData["Title"] = "Danh sách khách hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Danh sách khách hàng</h1>
        <a asp-area="Admin" asp-controller="Users" asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm mới
        </a>
    </div>

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Họ tên</th>
                            <th>Email</th>
                            <th>Số điện thoại</th>
                            <th>Địa chỉ</th>
                            <th>Hạng</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.UserId</td>
                                <td>@item.FullName</td>
                                <td>@item.Email</td>
                                <td>@item.Phone</td>
                                <td>@item.Address</td>
                                <td>@(item.Rank?.RankName ?? "Chưa có hạng")</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-area="Admin" asp-controller="Users" asp-action="Edit" asp-route-id="@item.UserId" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Users" asp-action="Details" asp-route-id="@item.UserId" class="btn btn-info btn-sm">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Users" asp-action="Delete" asp-route-id="@item.UserId" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#dataTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[0, 'desc']], // Sắp xếp theo ID giảm dần
                columnDefs: [
                    { orderable: false, targets: [6] } // Không cho phép sắp xếp cột thao tác
                ]
            });
        });
    </script>
} 