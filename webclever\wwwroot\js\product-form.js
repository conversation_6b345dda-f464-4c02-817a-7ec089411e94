$(document).ready(function () {
    // Image preview (an toàn hơn) - specific to product forms now
    $('.custom-file-input').on('change', function () {
        const fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);

        // Chỉ hiển thị preview nếu có phần tử #imagePreview
        if ($('#imagePreview').length && this.files && this.files.length > 0) {
            const reader = new FileReader();
            reader.onload = function (e) {
                $('#imagePreview').attr('src', e.target.result);
            }
            reader.readAsDataURL(this.files[0]);
        }
    });

    // Xử lý form submit cho form sản phẩm (sử dụng data attribute)
    $('form[data-form-type="product-ajax-form"]').on('submit', function (e) {
        e.preventDefault();
        console.log('Product form submit started...');

        const $form = $(this);
        const formData = new FormData();

        // 1. Thêm các trường form thông thường
        console.log('Adding basic form fields...');
        $form.find('input:not([type="file"]), select, textarea').each(function() {
            const $this = $(this);
            const name = $this.attr('name');
            const value = $this.val();
            if (name) {
                console.log(`Adding field: ${name} = ${value}`);
                formData.append(name, value);
            }
        });

        // 2. Thêm hình ảnh sản phẩm chính (chỉ khi #imageFile tồn tại và có file)
        const mainImageInput = $form.find('#imageFile'); // Tìm trong form hiện tại
        if (mainImageInput.length && mainImageInput[0].files && mainImageInput[0].files.length > 0) {
            const mainImageFile = mainImageInput[0].files[0];
            console.log('Adding main product image:', mainImageFile.name);
            formData.append('imageFile', mainImageFile);
        }

        // 3. Xử lý biến thể và hình ảnh biến thể (chỉ khi #variantsContainer tồn tại)
        console.log('Processing variants...');
        const $variantsContainer = $form.find('#variantsContainer');
        if ($variantsContainer.length) {
            $variantsContainer.find('.variant-item').each(function(index) {
                const $variantItem = $(this);
                console.log(`Processing variant ${index}:`);
                
                // Thêm thông tin biến thể
                const variantId = $variantItem.find('input[name$=".VariantId"]').val() || '0';
                const color = $variantItem.find('input[name$=".Color"]').val();
                const storage = $variantItem.find('input[name$=".Storage"]').val();
                const price = $variantItem.find('input[name$=".Price"]').val();
                const stock = $variantItem.find('input[name$=".Stock"]').val();

                console.log(`Variant ${index} data:`, {
                    VariantId: variantId,
                    Color: color,
                    Storage: storage,
                    Price: price,
                    Stock: stock
                });

                // Sửa lại cách append dữ liệu biến thể
                formData.append(`Variants[${index}].VariantId`, variantId);
                formData.append(`Variants[${index}].Color`, color || '');
                formData.append(`Variants[${index}].Storage`, storage || '');
                formData.append(`Variants[${index}].Price`, price || '0');
                formData.append(`Variants[${index}].Stock`, stock || '0');

                // Thêm hình ảnh biến thể nếu có (kiểm tra an toàn hơn)
                const variantImageInput = $variantItem.find('input[type="file"].variant-image-input'); // Chỉ chọn input file của biến thể
                if (variantImageInput.length && variantImageInput[0].files && variantImageInput[0].files.length > 0) {
                    const variantImageFile = variantImageInput[0].files[0];
                    console.log(`Adding variant ${index} image:`, variantImageFile.name);
                    formData.append(`VariantImages[${index}]`, variantImageFile);
                }
            });
        }

        // Log toàn bộ form data
        console.log('Final form data:');
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }

        // Gửi form data qua AJAX
        console.log('Sending AJAX request...');
        $.ajax({
            url: $form.attr('action'),
            type: $form.attr('method') || 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                console.log('AJAX success:', response);
                if (response.redirectUrl) {
                    window.location.href = response.redirectUrl;
                } else {
                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        window.location.href = '/Admin/Products'; 
                    });
                }
            },
            error: function (xhr, status, error) {
                console.error('AJAX error:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                let errorMessage = 'Đã xảy ra lỗi khi gửi dữ liệu.';
                if (xhr.responseJSON) {
                    if (xhr.responseJSON.errors) {
                        errorMessage = Object.values(xhr.responseJSON.errors).flat().join('<br>');
                    } else if (xhr.responseJSON.title) {
                        errorMessage = xhr.responseJSON.title;
                    } else if (xhr.responseJSON.detail) {
                        errorMessage = xhr.responseJSON.detail;
                    }
                } else if (xhr.responseText) {
                    errorMessage = xhr.responseText;
                }
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    html: errorMessage
                });
            }
        });
    });

    // Xử lý thêm biến thể (chỉ khi #variantsContainer tồn tại)
    if ($('#variantsContainer').length) {
        let variantCount = $('#variantsContainer .variant-item').length; // Khởi tạo với số lượng biến thể hiện có

        $('#addVariant').off('click').on('click', function () { // Sử dụng off().on() để tránh gắn nhiều sự kiện
            const variantHtml = `
                <div class="variant-item border p-3 mb-3">
                    <input type="hidden" name="Variants[${variantCount}].VariantId" value="0" />
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Màu sắc</label>
                                <input type="text" class="form-control" name="Variants[${variantCount}].Color" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Dung lượng</label>
                                <input type="text" class="form-control" name="Variants[${variantCount}].Storage" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Giá</label>
                                <input type="number" class="form-control" name="Variants[${variantCount}].Price" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Tồn kho</label>
                                <input type="number" class="form-control" name="Variants[${variantCount}].Stock" required>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-danger btn-sm btn-block remove-variant">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Hình ảnh biến thể</label>
                                <input type="file" class="form-control variant-image-input" name="VariantImages[${variantCount}]" accept="image/*">
                                <small class="text-muted">Hình ảnh sẽ hiển thị màu sắc cụ thể của biến thể</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#variantsContainer').append(variantHtml);
            variantCount++;
        });

        // Xử lý xóa biến thể
        $(document).on('click', '.remove-variant', function () {
            $(this).closest('.variant-item').remove();
            // Cập nhật lại index sau khi xóa (quan trọng cho form data)
            $('#variantsContainer .variant-item').each(function(index) {
                $(this).find('input, select, textarea').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        $(this).attr('name', name.replace(/\[\d+\]/g, `[${index}]`));
                    }
                });
            });
        });
    }

    // Xử lý hiển thị preview hình ảnh sản phẩm chính
    $('#imageFile').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#imagePreview').attr('src', e.target.result);
            }
            reader.readAsDataURL(file);
        }
    });

    // Hàm tạo HTML cho biến thể mới
    function createVariantHtml(index) {
        return `
            <div class="variant-item card mb-3" data-variant-index="${index}">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="control-label">Màu sắc</label>
                                <input type="text" name="Variants[${index}].Color" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="control-label">Dung lượng</label>
                                <input type="text" name="Variants[${index}].Storage" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="control-label">Giá</label>
                                <input type="number" name="Variants[${index}].Price" class="form-control" min="0" step="0.01" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="control-label">Số lượng</label>
                                <input type="number" name="Variants[${index}].Stock" class="form-control" min="0" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="control-label">Hình ảnh</label>
                                <input type="file" name="VariantImages" class="form-control variant-image" accept="image/*" />
                                <img src="/images/no-image.png" class="variant-preview mt-2" style="max-width: 100px;" />
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-danger btn-sm remove-variant mt-2">
                        <i class="fas fa-trash"></i> Xóa biến thể
                    </button>
                </div>
            </div>
        `;
    }

    // Thêm biến thể mới
    $('#addVariant').click(function() {
        const index = $('.variant-item').length;
        $('#variantsContainer').append(createVariantHtml(index));
    });

    // Xóa biến thể
    $(document).on('click', '.remove-variant', function() {
        $(this).closest('.variant-item').remove();
        // Cập nhật lại index cho các biến thể còn lại
        $('.variant-item').each(function(index) {
            $(this).attr('data-variant-index', index);
            $(this).find('input, select').each(function() {
                const name = $(this).attr('name');
                if (name) {
                    $(this).attr('name', name.replace(/\[\d+\]/, `[${index}]`));
                }
            });
        });
    });

    // Xử lý preview hình ảnh cho biến thể
    $(document).on('change', '.variant-image', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            const preview = $(this).siblings('.variant-preview');
            reader.onload = function(e) {
                preview.attr('src', e.target.result);
            }
            reader.readAsDataURL(file);
        }
    });
}); 