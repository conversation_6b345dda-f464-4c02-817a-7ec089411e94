﻿@model List<webclever.Models.Category>

@{
    ViewData["Title"] = "index";
    Layout = "~/Views/Shared/_Layout.cshtml";

    // Function to get image path for category
    string GetCategoryImage(string categoryName)
    {
        if (string.IsNullOrEmpty(categoryName)) return "/iphone.jpg";

        var name = categoryName.ToLower().Trim();

        if (name.Contains("iphone")) return "/iphone.jpg";
        if (name.Contains("mac")) return "/macbook.jpg";
        if (name.Contains("ipad")) return "/ipad.jpg";
        if (name.Contains("watch")) return "/appwatch.png";
        if (name.Contains("tai nghe") || name.Contains("loa")) return "/airpod.jpg";
        if (name.Contains("phụ kiện") || name.Contains("phu kien") || name.Contains("phukien")) return "/phukien.jpg";

        return "/iphone.jpg"; // Default fallback
    }
}

<!-- Category Navigation -->
<div class="home-container">
    @if (Model != null && Model.Any())
    {
        @foreach (var category in Model)
        {
            var imagePath = GetCategoryImage(category.Name ?? "");

            <a href="@Url.Action("Category", "Products", new { id = category.CategoryId })" class="home-product-card">
                <div class="home-product-image">
                    <img src="@imagePath" alt="@category.Name" onerror="this.style.border='2px solid red'; this.alt='Image not found: @imagePath';" />
                </div>
                <div class="home-product-title">@category.Name</div>
            </a>
        }
    }
</div>
<div class="flashsale-header">
    <div class="flashsale-container">
        <div class="flashsale-title">
            <img src="~/flashsale-logo.png" alt="Flash Sale" />
            <span>FLASH SALE</span>
        </div>
        <div class="countdown-wrapper">
            <span>KẾT THÚC TRONG</span>
            <div id="countdown">00 : 00 : 00</div>
        </div>
        <div class="time-info">
            <div>
                <span>Đang diễn ra</span>
                <div>08:00 - 23:59</div>
            </div>
            <div>
                <span>Ngày mai</span>
                <div>08:00 - 23:59</div>
            </div>
        </div>
    </div>
</div>
<!-- Products by Category -->
@if (Model != null && Model.Any())
{
    var categoryProducts = ViewBag.CategoryProducts as Dictionary<int, List<webclever.Models.Product>>;

    @foreach (var category in Model)
    {
        var products = categoryProducts?.ContainsKey(category.CategoryId) == true ? categoryProducts[category.CategoryId] : new List<webclever.Models.Product>();

        if (products.Any())
        {
            @* Category Header *@
            <div class="iphone-container">
                <a href="@Url.Action("Category", "Products", new { id = category.CategoryId })" class="iphone-link">
                    <div class="iphone-header">
                        @if (category.Name?.ToLower() != "tai nghe, loa" && category.Name?.ToLower() != "phụ kiện")
                        {
                            <img src="~/logo-apple.png" alt="Apple" class="apple-logo">
                        }
                        <h1>@category.Name</h1>
                    </div>
                </a>
            </div>
           
            @* Products Carousel *@
            <div class="carousel-container" data-category="@category.CategoryId">
                <button class="arrow left" onclick="moveCarousel(@category.CategoryId, -1)">&#8592;</button>
                <div class="carousel-wrapper">
                    <div class="carousel-track" id="<EMAIL>">
                        @foreach (var product in products)
                        {
                            // Lấy giá thấp nhất và cao nhất trong các biến thể
                            decimal minPrice = 0;
                            decimal maxPrice = 0;
                            if (product.ProductVariants != null && product.ProductVariants.Any())
                            {
                                minPrice = product.ProductVariants.Min(v => v.Price ?? 0);
                                maxPrice = product.ProductVariants.Max(v => v.Price ?? 0);
                            }
                            // Ảnh sản phẩm: ưu tiên Product.ImageUrl, nếu không có thì lấy từ biến thể đầu tiên
                            string imageUrl = !string.IsNullOrEmpty(product.ImageUrl) ? product.ImageUrl :
                                (product.ProductVariants != null && product.ProductVariants.Any() && !string.IsNullOrEmpty(product.ProductVariants.First().ImageUrl)
                                    ? product.ProductVariants.First().ImageUrl : "/images/no-image.png");
                            // Tính phần trăm giảm giá nếu có
                            int discount = (maxPrice > minPrice && maxPrice > 0) ? (int)Math.Round((maxPrice - minPrice) / maxPrice * 100) : 0;
                            <div class="carousel-card">
                                <a href="/Products/Details/@product.ProductId" style="text-decoration: none; color: inherit;">
                                    <div class="carousel-image">
                                        <img src="@imageUrl" alt="@product.Name" onerror="this.src='/images/no-image.png';" />
                                    </div>
                                    <div class="carousel-info">
                                        <h3 class="carousel-title">@product.Name</h3>
                                        <div class="carousel-pricing">
                                            <!-- Giá hiện tại -->
                                            <div class="current-price">@string.Format("{0:N0}", minPrice)₫</div>
                                            <!-- Giá gốc và giảm giá nếu có -->
                                            @if (maxPrice > minPrice && maxPrice > 0)
                                            {
                                                <div class="original-price">@string.Format("{0:N0}", maxPrice)₫</div>
                                                <div class="discount">-@discount%</div>
                                            }
                                        </div>
                                        <div class="online-price">Online giá rẻ quá</div>
                                    </div>
                                </a>
                            </div>
                        }
                    </div>
                </div>
                <button class="arrow right" onclick="moveCarousel(@category.CategoryId, 1)">&#8594;</button>
            </div>
            <div class="indicator" id="<EMAIL>"></div>
        }
    }
}

<script>
// Carousel functionality
let carouselPositions = {};

function initializeCarousels() {
    document.querySelectorAll('.carousel-container').forEach(container => {
        const categoryId = container.dataset.category;
        carouselPositions[categoryId] = 0;

        // Initialize indicators
        const track = container.querySelector('.carousel-track');
        const cards = track.querySelectorAll('.carousel-card');
        const indicator = document.getElementById(`indicator-${categoryId}`);

        // Calculate how many cards fit in view
        const containerWidth = container.querySelector('.carousel-wrapper').offsetWidth;
        const cardWidth = 320; // card width + gap
        const visibleCards = Math.floor(containerWidth / cardWidth);
        const totalPages = Math.ceil(cards.length / visibleCards);

        // Create indicator dots
        indicator.innerHTML = '';
        for (let i = 0; i < totalPages; i++) {
            const dot = document.createElement('div');
            dot.className = `indicator-dot ${i === 0 ? 'active' : ''}`;
            dot.onclick = () => goToPage(categoryId, i);
            indicator.appendChild(dot);
        }
    });
}

function moveCarousel(categoryId, direction) {
    const container = document.querySelector(`[data-category="${categoryId}"]`);
    const track = container.querySelector('.carousel-track');
    const cards = track.querySelectorAll('.carousel-card');
    const containerWidth = container.querySelector('.carousel-wrapper').offsetWidth;
    const cardWidth = 320;
    const visibleCards = Math.floor(containerWidth / cardWidth);
    const totalPages = Math.ceil(cards.length / visibleCards);

    carouselPositions[categoryId] += direction;

    if (carouselPositions[categoryId] < 0) {
        carouselPositions[categoryId] = totalPages - 1;
    } else if (carouselPositions[categoryId] >= totalPages) {
        carouselPositions[categoryId] = 0;
    }

    updateCarousel(categoryId);
}

function goToPage(categoryId, page) {
    carouselPositions[categoryId] = page;
    updateCarousel(categoryId);
}

function updateCarousel(categoryId) {
    const container = document.querySelector(`[data-category="${categoryId}"]`);
    const track = container.querySelector('.carousel-track');
    const containerWidth = container.querySelector('.carousel-wrapper').offsetWidth;
    const cardWidth = 320;
    const visibleCards = Math.floor(containerWidth / cardWidth);
    const currentPage = carouselPositions[categoryId];

    const translateX = -(currentPage * visibleCards * cardWidth);
    track.style.transform = `translateX(${translateX}px)`;

    // Update indicators
    const indicator = document.getElementById(`indicator-${categoryId}`);
    const dots = indicator.querySelectorAll('.indicator-dot');
    dots.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentPage);
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', initializeCarousels);

// Reinitialize on window resize
window.addEventListener('resize', initializeCarousels);
</script>


@* <div> *@
@*     @if (ViewData["ProductImages"] is List<webclever.Models.ProductImage> images) *@
@*     { *@
@*         <div class="product-gallery"> *@
@*             @foreach (var item in images) *@
@*             { *@
@*                 <img src="@item.ImageUrl" alt="Product Image" /> *@
@*             } *@
@*         </div> *@
@*     } *@
@* </div> *@









