using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webclever.Models;

namespace webclever.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class WishlistsController : Controller
    {
        private readonly WebcleverContext _context;

        public WishlistsController(WebcleverContext context)
        {
            _context = context;
        }

        // GET: Admin/Wishlists
        public async Task<IActionResult> Index()
        {
            var wishlists = await _context.Wishlists
                .Include(w => w.User)
                .Include(w => w.Variant)
                    .ThenInclude(pv => pv.Product)
                .OrderByDescending(w => w.AddedAt)
                .ToListAsync();
            return View(wishlists);
        }

        // GET: Admin/Wishlists/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var wishlist = await _context.Wishlists
                .Include(w => w.User)
                .Include(w => w.Variant)
                    .ThenInclude(v => v.Product)
                .FirstOrDefaultAsync(m => m.WishlistId == id);

            if (wishlist == null)
            {
                return NotFound();
            }

            return View(wishlist);
        }

        // GET: Admin/Wishlists/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var wishlist = await _context.Wishlists
                .Include(w => w.User)
                .Include(w => w.Variant)
                    .ThenInclude(v => v.Product)
                .FirstOrDefaultAsync(m => m.WishlistId == id);

            if (wishlist == null)
            {
                return NotFound();
            }

            return View(wishlist);
        }

        // POST: Admin/Wishlists/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var wishlist = await _context.Wishlists.FindAsync(id);
            if (wishlist != null)
            {
                _context.Wishlists.Remove(wishlist);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction(nameof(Index));
        }

        private bool WishlistExists(int id)
        {
            return _context.Wishlists.Any(e => e.WishlistId == id);
        }
    }
} 