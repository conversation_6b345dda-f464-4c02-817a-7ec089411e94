﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webclever.Models;

/// <summary>
/// Model đại diện cho người dùng trong hệ thống
/// </summary>
public partial class User
{
    [Key]
    public int UserId { get; set; }

    [Required(ErrorMessage = "Họ tên không được để trống")]
    [StringLength(100, ErrorMessage = "Họ tên không được vượt quá 100 ký tự")]
    [Display(Name = "Họ tên")]
    public string? FullName { get; set; }

    [Required(ErrorMessage = "Email không được để trống")]
    [EmailAddress(ErrorMessage = "Email không hợp lệ")]
    [StringLength(255, ErrorMessage = "Email không được vượt quá 255 ký tự")]
    [Display(Name = "Email")]
    public string? Email { get; set; }

    [Required(ErrorMessage = "Mật khẩu không được để trống")]
    [StringLength(255, ErrorMessage = "Mật khẩu không được vượt quá 255 ký tự")]
    [Display(Name = "Mật khẩu")]
    public string? PasswordHash { get; set; }

    [Phone(ErrorMessage = "Số điện thoại không hợp lệ")]
    [StringLength(20, ErrorMessage = "Số điện thoại không được vượt quá 20 ký tự")]
    [Display(Name = "Số điện thoại")]
    public string? Phone { get; set; }

    [StringLength(255, ErrorMessage = "Địa chỉ không được vượt quá 255 ký tự")]
    [Display(Name = "Địa chỉ")]
    public string? Address { get; set; }

    [Range(0, 2, ErrorMessage = "Giới tính không hợp lệ")]
    [Display(Name = "Giới tính")]
    public int? Gender { get; set; }

    [Required(ErrorMessage = "Vai trò không được để trống")]
    [StringLength(20, ErrorMessage = "Vai trò không được vượt quá 20 ký tự")]
    [Display(Name = "Vai trò")]
    public string? Role { get; set; }

    [Display(Name = "Hạng khách hàng")]
    public int? RankId { get; set; }

    [Display(Name = "Ngày tạo")]
    public DateTime? CreatedAt { get; set; }

    // Navigation properties
    public virtual ICollection<Cart> Carts { get; set; } = new List<Cart>();
    public virtual ICollection<Order> Orders { get; set; } = new List<Order>();
    public virtual CustomerRank? Rank { get; set; }
    public virtual ICollection<Review> Reviews { get; set; } = new List<Review>();
    public virtual ICollection<Wishlist> Wishlists { get; set; } = new List<Wishlist>();
}
