@model webclever.Models.Product

@{
    ViewData["Title"] = "Chi tiết sản phẩm";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chi tiết sản phẩm</h1>
        <div>
            <a asp-action="Edit" asp-route-id="@Model.ProductId" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
            </a>
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><PERSON><PERSON><PERSON> <PERSON><PERSON> sản phẩm</h6>
                </div>
                <div class="card-body text-center">
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid" style="max-height: 300px;" />
                    }
                    else
                    {
                        <img src="/images/no-image.png" alt="No image" class="img-fluid" style="max-height: 300px;" />
                    }
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin sản phẩm</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">Tên sản phẩm</dt>
                        <dd class="col-sm-9">@Model.Name</dd>

                        <dt class="col-sm-3">Thương hiệu</dt>
                        <dd class="col-sm-9">@Model.Brand</dd>

                        <dt class="col-sm-3">Danh mục</dt>
                        <dd class="col-sm-9">@Model.Category?.Name</dd>

                        <dt class="col-sm-3">Mô tả</dt>
                        <dd class="col-sm-9">@Model.Description</dd>

                        <dt class="col-sm-3">Ngày tạo</dt>
                        <dd class="col-sm-9">@(Model.CreatedAt.HasValue ? string.Format("{0:dd/MM/yyyy HH:mm}", Model.CreatedAt.Value) : "N/A")</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Biến thể sản phẩm</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Màu sắc</th>
                            <th>Dung lượng</th>
                            <th>Giá</th>
                            <th>Tồn kho</th>
                            <th>Hình ảnh</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.ProductVariants != null && Model.ProductVariants.Any())
                        {
                            foreach (var variant in Model.ProductVariants)
                            {
                                <tr>
                                    <td>@variant.Color</td>
                                    <td>@variant.Storage</td>
                                    <td>@(variant.Price.HasValue ? string.Format("{0:N0} đ", variant.Price.Value) : "N/A")</td>
                                    <td>
                                        <span class="badge bg-@(variant.Stock < 10 ? "danger" : "success")">
                                            @variant.Stock
                                        </span>
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(variant.ImageUrl))
                                        {
                                            <img src="@variant.ImageUrl" alt="@variant.Color" style="width: 100px; height: 100px; object-fit: cover;" class="img-thumbnail" />
                                        }
                                        else
                                        {
                                            <img src="/images/no-image.png" alt="No image" style="width: 100px; height: 100px; object-fit: cover;" class="img-thumbnail" />
                                        }
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="5" class="text-center">Chưa có biến thể nào</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div> 