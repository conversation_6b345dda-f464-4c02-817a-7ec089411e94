﻿

.search-title {
    text-align: center;
    color: #f6f6f6;
    margin-top: 32px;
    margin-bottom: 32px;
    font-size: 2rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.no-results-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 32px;
}

.no-results-content {
    background: #333;
    color: #fff;
    border-radius: 20px;
    padding: 32px 48px;
    min-width: 540px;
    max-width: 700px;
    text-align: center;
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

    .no-results-content p {
        font-weight: bold;
        margin-bottom: 16px;
    }

    .no-results-content ul {
        list-style: disc inside;
        text-align: left;
        margin: 0 auto;
        display: inline-block;
        padding-left: 0;
        color: #eaeaea;
    }

.feedback-box {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 8px;
    padding: 18px 28px 12px 28px;
    min-width: 380px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.09);
    text-align: center;
}

    .feedback-box p {
        margin-bottom: 10px;
        color: #444;
        font-size: 1rem;
    }

.feedback-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 3px;
}

    .feedback-buttons .btn {
        background: none;
        border: none;
        outline: none;
        font-size: 1.1rem;
        cursor: pointer;
        padding: 7px 16px;
        border-radius: 6px;
        transition: background 0.13s;
    }

    .feedback-buttons .btn-happy {
        color: #e38c02;
    }

    .feedback-buttons .btn-neutral {
        color: #e1b93b;
    }

        .feedback-buttons .btn-happy:hover,
        .feedback-buttons .btn-neutral:hover {
            background: #f5f5f5;
        }

/* Search results */
.search-results {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 32px;
    margin: 32px auto 0 auto;
    max-width: 1200px;
}

.product-card {
    background: #2c2c2c;
    color: #fff;
    border-radius: 12px;
    width: 300px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 18px 0 12px 0 !important;
    transition: transform .12s;
}

    .product-card img {
        width: auto;
        height: 260px;
        object-fit: contain;
        margin-bottom: 10px;
        background: #333;
        border-radius: 8px;
    }

    .product-card p {
        margin: 0;
        color: #fff;
        font-size: 1rem;
        text-align: center;
    }

@media (max-width: 700px) {
    .no-results-content {
        min-width: unset;
        max-width: 95vw;
        padding: 18px 6vw;
    }

    .feedback-box {
        min-width: unset;
        width: 95vw;
        padding: 10px 6vw 6px 6vw;
    }

    .search-title {
        font-size: 1.15rem;
    }

    .product-card {
        width: 46vw;
    }
}
