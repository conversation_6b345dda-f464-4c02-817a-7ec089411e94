@model webclever.Models.Product

@{
    ViewData["Title"] = "Chỉnh sửa sản phẩm";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chỉnh sửa sản phẩm</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            @if (ViewData["VariantImageStatus"] != null)
            {
                <div class="alert alert-info" role="alert">
                    @ViewData["VariantImageStatus"]
                </div>
            }
            <form asp-action="Edit" asp-route-id="@Model.ProductId" method="post" enctype="multipart/form-data" class="needs-validation" novalidate data-form-type="product-ajax-form">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" name="ProductId" value="@Model.ProductId" />

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <label asp-for="Name" class="control-label">Tên sản phẩm</label>
                            <input asp-for="Name" class="form-control" required />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Brand" class="control-label">Thương hiệu</label>
                            <input asp-for="Brand" class="form-control" required />
                            <span asp-validation-for="Brand" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="CategoryId" class="control-label">Danh mục</label>
                            <select asp-for="CategoryId" class="form-control" asp-items="ViewBag.CategoryId" required>
                                <option value="">-- Chọn danh mục --</option>
                            </select>
                            <span asp-validation-for="CategoryId" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Description" class="control-label">Mô tả</label>
                            <textarea asp-for="Description" class="form-control" rows="4"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label class="control-label">Hình ảnh sản phẩm</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="imageFile" name="imageFile" accept="image/*">
                                <label class="custom-file-label" for="imageFile">Chọn file...</label>
                            </div>
                            <div class="mt-2">
                                <img id="imagePreview" src="@(string.IsNullOrEmpty(Model.ImageUrl) ? "/images/no-image.png" : Model.ImageUrl)" 
                                     alt="Preview" class="img-thumbnail" style="max-width: 200px;" />
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="my-4" />

                <div class="mb-3">
                    <h5>Biến thể sản phẩm</h5>
                    <button type="button" id="addVariant" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> Thêm biến thể
                    </button>
                </div>

                <div id="variantsContainer">
                    @if (Model.ProductVariants != null && Model.ProductVariants.Any())
                    {
                        var index = 0;
                        foreach (var variant in Model.ProductVariants)
                        {
                            <div class="variant-item border p-3 mb-3">
                                <input type="hidden" name="Variants[@index].VariantId" value="@variant.VariantId" />
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Màu sắc</label>
                                            <input type="text" class="form-control" name="Variants[@index].Color" value="@variant.Color">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Dung lượng</label>
                                            <input type="text" class="form-control" name="Variants[@index].Storage" value="@variant.Storage">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Giá</label>
                                            <input type="number" class="form-control" name="Variants[@index].Price" value="@variant.Price">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Tồn kho</label>
                                            <input type="number" class="form-control" name="Variants[@index].Stock" value="@variant.Stock">
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <button type="button" class="btn btn-danger btn-block remove-variant">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Hình ảnh biến thể</label>
                                            @if (!string.IsNullOrEmpty(variant.ImageUrl))
                                            {
                                                <div class="mb-2">
                                                    <img src="@variant.ImageUrl" alt="@variant.Color" style="max-width: 100px; max-height: 100px; object-fit: cover;" class="img-thumbnail" />
                                                </div>
                                            }
                                            <input type="file" class="form-control variant-image-input" name="VariantImages[@index]" accept="image/*">
                                            <small class="text-muted">Hình ảnh sẽ hiển thị màu sắc cụ thể của biến thể</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            index++;
                        }
                    }
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/js/product-form.js"></script>
    <script>
        $(document).ready(function() {
            // Xử lý hiển thị tên file khi chọn file
            $('.custom-file-input').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);
                
                // Hiển thị preview hình ảnh
                if (this.files && this.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#imagePreview').attr('src', e.target.result);
                    }
                    reader.readAsDataURL(this.files[0]);
                }
            });

            // Xử lý thêm biến thể mới
            $('#addVariant').click(function() {
                var index = $('.variant-item').length;
                var variantHtml = `
                    <div class="variant-item border p-3 mb-3">
                        <input type="hidden" name="Variants[${index}].VariantId" value="0" />
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Màu sắc</label>
                                    <input type="text" class="form-control" name="Variants[${index}].Color">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Dung lượng</label>
                                    <input type="text" class="form-control" name="Variants[${index}].Storage">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Giá</label>
                                    <input type="number" class="form-control" name="Variants[${index}].Price">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Tồn kho</label>
                                    <input type="number" class="form-control" name="Variants[${index}].Stock">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn btn-danger btn-block remove-variant">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Hình ảnh biến thể</label>
                                    <input type="file" class="form-control variant-image-input" name="VariantImages[${index}]" accept="image/*">
                                    <small class="text-muted">Hình ảnh sẽ hiển thị màu sắc cụ thể của biến thể</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                $('#variantsContainer').append(variantHtml);
            });

            // Xử lý xóa biến thể
            $(document).on('click', '.remove-variant', function() {
                $(this).closest('.variant-item').remove();
            });

            // Xử lý preview hình ảnh biến thể
            $(document).on('change', '.variant-image-input', function() {
                if (this.files && this.files[0]) {
                    var reader = new FileReader();
                    var img = $(this).closest('.variant-item').find('img');
                    if (img.length === 0) {
                        img = $('<img>').addClass('img-thumbnail').css({
                            'max-width': '100px',
                            'max-height': '100px',
                            'object-fit': 'cover'
                        });
                        $(this).before(img);
                    }
                    reader.onload = function(e) {
                        img.attr('src', e.target.result);
                    }
                    reader.readAsDataURL(this.files[0]);
                }
            });
        });
    </script>
} 