using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace webclever.Areas.Admin.Controllers
{
    [Authorize(Roles = "Admin")] // Yêu cầu xác thực và phải có vai trò Admin
    public class BaseController : Controller
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            base.OnActionExecuting(context);

            // // Kiểm tra session (Đã được xử lý bởi [Authorize] attribute)
            // var userId = HttpContext.Session.GetString("UserId");
            // var userRole = HttpContext.Session.GetString("UserRole");

            // // Nếu chưa đăng nhập hoặc không phải admin
            // if (string.IsNullOrEmpty(userId) || userRole != "Admin")
            // {
            //     // Chuyển hướng về trang đăng nhập
            //     context.Result = RedirectToAction("Login", "Account", new { area = "" });
            //     return;
            // }

            // L<PERSON><PERSON> thông tin user vào ViewBag để sử dụng trong view (Dữ liệu giả định)
            ViewBag.UserId = HttpContext.Session.GetString("UserId") ?? User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "";
            ViewBag.UserName = HttpContext.Session.GetString("UserName") ?? User.Identity?.Name ?? "";
            ViewBag.UserEmail = HttpContext.Session.GetString("UserEmail") ?? User.FindFirst(ClaimTypes.Email)?.Value ?? "";
            ViewBag.UserRole = HttpContext.Session.GetString("UserRole") ?? User.FindFirst(ClaimTypes.Role)?.Value ?? "";
        }
    }
} 