@model IEnumerable<webclever.Models.Wishlist>

@{
    ViewData["Title"] = "Danh sách yêu thích";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Danh sách yêu thích</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Kh<PERSON>ch hàng</th>
                            <th>Sản phẩm</th>
                            <th>Phân loại</th>
                            <th>Ng<PERSON>y thêm</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.WishlistId</td>
                                <td>
                                    <a asp-controller="Users" asp-action="Details" asp-route-id="@item.UserId">
                                        @item.User?.FullName
                                    </a>
                                </td>
                                <td>
                                    <a asp-controller="Products" asp-action="Details" asp-route-id="@item.Variant?.ProductId">
                                        @item.Variant?.Product?.Name
                                    </a>
                                </td>
                                <td>
                                    @if (item.Variant != null)
                                    {
                                        <span class="badge bg-info">@item.Variant.Color - @item.Variant.Storage</span>
                                    }
                                </td>
                                <td>
                                    @(item.AddedAt.HasValue ? string.Format("{0:dd/MM/yyyy}", item.AddedAt.Value) : "N/A")
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.WishlistId" class="btn btn-info btn-sm">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.WishlistId" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('.datatable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/vi.json'
                }
            });
        });
    </script>
} 