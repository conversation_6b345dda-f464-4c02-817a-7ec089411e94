@model IEnumerable<webclever.Models.Order>

@{
    ViewData["Title"] = "Quản lý đơn hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <h1 class="mt-4">Quản lý đơn hàng</h1>

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">
            @TempData["Error"]
        </div>
    }

    <div class="card mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="ordersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Mã đơn</th>
                            <th>Kh<PERSON>ch hàng</th>
                            <th>Ngày đặt</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.OrderId</td>
                                <td>
                                    @item.User?.FullName
                                    <br />
                                    <small class="text-muted">@item.User?.Email</small>
                                </td>
                                <td>@item.OrderDate?.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>@item.TotalAmount?.ToString("N0") đ</td>
                                <td>
                                    <span class="badge @(item.OrderStatus switch {
                                        "Pending" => "bg-warning",
                                        "Processing" => "bg-info",
                                        "Shipped" => "bg-primary",
                                        "Delivered" => "bg-success",
                                        "Cancelled" => "bg-danger",
                                        _ => "bg-secondary"
                                    })">
                                        @item.OrderStatus
                                    </span>
                                </td>
                                <td>
                                    <a asp-action="Details" asp-route-id="@item.OrderId" class="btn btn-info btn-sm">
                                        <i class="fas fa-info-circle"></i> Chi tiết
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.OrderId" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i> Sửa
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.OrderId" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> Xóa
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#ordersTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Vietnamese.json"
                },
                "order": [[2, "desc"]] // Sắp xếp theo ngày đặt giảm dần
            });
        });
    </script>
} 