@model webclever.Models.Category

@{
    ViewData["Title"] = "Chỉnh sửa danh mục";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chỉnh sửa danh mục</h1>
        <a asp-area="Admin" asp-controller="Categories" asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form id="categoryForm" asp-area="Admin" asp-controller="Categories" asp-action="Edit" asp-route-id="@Model.CategoryId" method="post">
                @Html.AntiForgeryToken()
                <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                <input type="hidden" asp-for="CategoryId" />

                <div class="form-group mb-3">
                    <label asp-for="Name" class="control-label">Tên danh mục <span class="text-danger">*</span></label>
                    <input asp-for="Name" class="form-control" required />
                    <span asp-validation-for="Name" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Description" class="control-label">Mô tả</label>
                    <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function () {
            // Xử lý submit form
            $('#categoryForm').submit(function (e) {
                e.preventDefault();
                
                // Vô hiệu hóa nút submit để tránh double submit
                $(this).find('button[type="submit"]').prop('disabled', true);
                
                // Lấy dữ liệu form
                var formData = $(this).serialize();
                
                // Gửi request AJAX
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    success: function (response) {
                        if (response.success) {
                            // Hiển thị thông báo thành công
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: 'Danh mục đã được cập nhật thành công.',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                // Chuyển hướng về trang danh sách
                                window.location.href = '@Url.Action("Index", "Categories", new { area = "Admin" })';
                            });
                        } else {
                            // Hiển thị thông báo lỗi
                            if (response.errors) {
                                // Xử lý lỗi validation
                                let errorMessage = '';
                                for (let key in response.errors) {
                                    errorMessage += response.errors[key].join('<br>') + '<br>';
                                }
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Lỗi!',
                                    html: errorMessage
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Lỗi!',
                                    text: response.message || 'Đã xảy ra lỗi khi cập nhật danh mục.'
                                });
                            }
                            // Kích hoạt lại nút submit
                            $('#categoryForm').find('button[type="submit"]').prop('disabled', false);
                        }
                    },
                    error: function (xhr, status, error) {
                        // Hiển thị thông báo lỗi
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: 'Đã xảy ra lỗi khi xử lý yêu cầu: ' + error
                        });
                        // Kích hoạt lại nút submit
                        $('#categoryForm').find('button[type="submit"]').prop('disabled', false);
                    }
                });
            });
        });
    </script>
} 