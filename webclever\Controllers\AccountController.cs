using Microsoft.AspNetCore.Mvc;
using webclever.Models;
using System.Linq;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;

namespace webclever.Controllers
{
    [AllowAnonymous]
    public class AccountController : Controller
    {
        private readonly WebcleverContext _context;

        public AccountController(WebcleverContext context)
        {
            _context = context;
        }

        public IActionResult Login()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Login(string email, string password)
        {
            // So sánh mật khẩu trực tiếp (không khuyến nghị về bảo mật)
            var user = _context.Users.FirstOrDefault(u => u.Email == email && u.PasswordHash == password);

            if (user != null)
            {
                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.NameIdentifier, user.UserId.ToString()),
                    new Claim(ClaimTypes.Email, user.Email),
                    new Claim(ClaimTypes.Role, user.Role)
                };

                var claimsIdentity = new ClaimsIdentity(
                    claims, CookieAuthenticationDefaults.AuthenticationScheme);

                var authProperties = new AuthenticationProperties
                {
                    // Cho phép làm mới phiên đăng nhập sau một thời gian,
                    // và liệu cookie có nên được lưu trữ vĩnh viễn hay không.
                    IsPersistent = true,
                    ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(30) // Ví dụ: hết hạn sau 30 phút
                };

                await HttpContext.SignInAsync(
                    CookieAuthenticationDefaults.AuthenticationScheme,
                    new ClaimsPrincipal(claimsIdentity),
                    authProperties);

                // Thêm thông báo gỡ lỗi để kiểm tra giá trị của user.Role
                TempData["UserRoleDebug"] = user.Role;

                if (user.Role == "Admin")
                {
                    TempData["RedirectTargetDebug"] = "Admin Area";
                    return RedirectToAction("Index", "Home", new { area = "Admin" }); // Chuyển hướng đến trang Admin
                }
                else // Giả sử các role khác là khách hàng hoặc role mặc định
                {
                    TempData["RedirectTargetDebug"] = "Home Index";
                    return RedirectToAction("Index", "Home"); // Chuyển hướng đến trang chủ
                }
            }
            else
            {
                ModelState.AddModelError(string.Empty, "Email hoặc mật khẩu không đúng.");
                return View();
            }
        }

        [HttpGet]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return RedirectToAction("Index", "Home"); // Chuyển hướng về trang chủ sau khi đăng xuất
        }
    }
} 