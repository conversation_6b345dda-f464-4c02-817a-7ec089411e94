﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - clever technology</title>
    <title>clever technology</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/master.css">
    <link href="/css/home.css" rel="stylesheet" />
    <link href="/css/banner.css" rel="stylesheet" />
    <link rel="stylesheet" href="~/css/flashsale.css" />
    <link rel="stylesheet" href="~/css/carousel.css" />
    <link rel="stylesheet" href="~/css/Secret.css" />
    <link rel="stylesheet" href="~/css/SearchResult.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="navbar">
        <a href="/" class="logo">
            <div class="logo-text">
                <span class="clever">Clever</span> <span class="technology">Technology</span>
                <img src="/premium reseller apple banner.PNG" alt="Apple Logo" class="logo-image" />
            </div>
        </a>
        <div class="nav-container">
            <div class="nav-links">
                @await Component.InvokeAsync("CategoryNavigation")
            </div>
        </div>
        <div class="nav-icons">
                        <!-- logo, menu, nav-icon... -->
                        <div class="nav-icon">
                            <!-- các icon -->
                            <button id="openSearchBtn">
                                <img src="https://img.icons8.com/ios-filled/24/ffffff/search--v1.png" alt="Search" />
                            </button>
                        </div>
                        <div>
                            <a href="/Cart" style="text-decoration: none; color: white; position: relative;">
                                <img src="https://img.icons8.com/ios-filled/50/ffffff/shopping-bag.png" alt="Cart" />
                                <span class="cart-count" style="position: absolute; top: -5px; right: -5px; background: #ff6b35; color: white; border-radius: 50%; width: 20px; height: 20px; font-size: 12px; display: flex; align-items: center; justify-content: center; font-weight: bold;">0</span>
                            </a>
                        </div>
                        @if (User.Identity.IsAuthenticated && User.IsInRole("Customer"))
                        {
                            <div class="dropdown">
                                <a class="dropdown-toggle login-icon" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                    <img src="https://img.icons8.com/ios-filled/50/ffffff/user.png" alt="User" />
                                </a>

                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink">
                                    <li><a class="dropdown-item" href="/Customer/Profile">Thông tin tài khoản</a></li>
                                    <li><a class="dropdown-item" href="/Customer/Orders">Đơn hàng đã đặt</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="/Account/Logout">Đăng xuất</a></li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <a href="/Account/Login" class="login-icon"><img src="https://img.icons8.com/ios-filled/50/ffffff/user.png" alt="Login" /></a>
                        }
                </div>
        </div>
    </div>

    <!-- Banner Section -->
    @if (ViewData["HideBanner"] == null || !(bool)ViewData["HideBanner"])
    {
        @await Component.InvokeAsync("Banner")
    }

    <main role="main" class="pb-3 container-fluid">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container-fluid">
            <div class="footer-logo">
                <span class="footer-brand">Clever <span class="highlight">Technology</span></span>
                <img src="https://www.apple.com/ac/structured-data/images/open_graph_logo.png" alt="Apple Logo" class="footer-apple-logo" />
                <span class="footer-reseller">Premium Reseller</span>
            </div>
            <div class="footer-sections">
                <div class="footer-section">
                    <h3><a> Tổng đài</a></h3>
                    <p><a href="https://example.com/mua-hang">Mua hàng: 1900.969642 (8:00 - 21:30)</a></p>
                    <p><a href="https://example.com/khieu-nai">Khếu nại: 1900.969643 (8:00 - 21:30)</a></p>
                    <p><a href="https://example.com/ket-noi">Kết nối với chúng tôi</a></p>
                    <div class="social-icons">
                        <a href="https://facebook.com"><img src="https://img.icons8.com/ios-filled/50/ffffff/facebook.png" alt="Facebook" /></a>
                        <a href="https://youtube.com"><img src="https://img.icons8.com/ios-filled/50/ffffff/youtube.png" alt="YouTube" /></a>
                        <a href="https://zalo.me"><img src="https://img.icons8.com/ios-filled/50/ffffff/zalo.png" alt="Zalo" /></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3><a>Hỗ trợ khách hàng</a></h3>
                    <p><a href="https://example.com/huong-dan-mua-hang">Hướng dẫn mua hàng online</a></p>
                    <p><a href="https://example.com/huong-dan-thanh-toan">Hướng dẫn thanh toán</a></p>
                    <p><a href="https://example.com/huong-dan-doi-tra">Hướng dẫn đổi trả</a></p>
                </div>
                <div class="footer-section">
                    <h3><a>Về thương hiệu Clever Technology</a></h3>
                    <p><a href="https://example.com/tich-diem">Tích điểm Quà tặng VIP</a></p>
                    <p><a href="https://example.com/gioi-thieu-clever-technology">Giới thiệu Clever Technology</a></p>
                </div>
                <div class="footer-section">
                    <h3><a>Trung tâm bảo hành Clever Technology</a></h3>
                    <p><a href="https://example.com/gioi-thieu-clever-technology-care">Giới thiệu Clever Technology</a></p>
                    <p><a href="https://example.com/huong-dan-ky-thuat">Bản hướng dẫn kỹ thuật</a></p>
                    <p><a href="https://example.com/chinh-sach-bao-hanh">Chính sách bảo hành & đổi trả</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>©2018. Công ty TNHH Thương Mại và Dịch Vụ Giải Pháp Số. GPDKKD: 0303217354 do sở KH & ĐT TPHCM cấp ngày 24/01/2007</p>
                <p>Địa chỉ: 128 Trần Quang Khải, P.Tân Bình, Q.1, TP. Hồ Chí Minh. Điện thoại: 028 38125960. Email: <EMAIL></p>
                <p>Đường D1, D2, D3, P.Tân Phú, TP.Thủ Đức, TP.Hồ Chí Minh. Điện thoại: 028 38125960. Email: <EMAIL></p>
                <p>Hỗ trợ: <EMAIL></p>
            </div>
        </div>
    </footer>

    <div class="search-overlay" id="searchOverlay">
        <form class="search-bar" id="searchBar" action="/Home/Search" method="get">
            <input type="text" name="query" placeholder="Tìm kiếm sản phẩm..." id="searchInputOverlay" required />
            <button type="submit" class="close-overlay" id="closeSearchOverlay">×</button>
        </form>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Scripts -->
    <script src="~/js/Search.js"></script>
    <script src="~/js/carousel.js"></script>
    <script src="~/js/banner.js"></script>
    <script src="~/js/home.js"></script>
    <script src="~/js/flashsale.js"></script>
    <script src="~/js/Secret.js"></script>

    <!-- Cart count update script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
        });

        function updateCartCount() {
            fetch('/Cart/GetCartCount')
            .then(response => response.json())
            .then(data => {
                const cartCountElement = document.querySelector('.cart-count');
                if (cartCountElement) {
                    cartCountElement.textContent = data.count;
                    cartCountElement.style.display = data.count > 0 ? 'flex' : 'none';
                }
            })
            .catch(error => {
                console.error('Error updating cart count:', error);
            });
        }
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
