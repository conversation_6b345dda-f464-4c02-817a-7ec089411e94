@model webclever.Models.Category

@{
    ViewData["Title"] = "Xóa danh mục";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">X<PERSON>a danh mục</h1>
        <a asp-area="Admin" asp-controller="Categories" asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="alert alert-danger">
                <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Cảnh báo!</h4>
                <p class="mb-0">Bạn có chắc chắn muốn xóa danh mục này? Hành động này không thể hoàn tác.</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0">Thông tin danh mục</h5>
                        </div>
                        <div class="card-body">
                            <dl class="row mb-0">
                                <dt class="col-sm-4">Tên danh mục</dt>
                                <dd class="col-sm-8">@Model.Name</dd>

                                <dt class="col-sm-4">Mô tả</dt>
                                <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.Description) ? "Không có mô tả" : Model.Description)</dd>

                                <dt class="col-sm-4">Số sản phẩm</dt>
                                <dd class="col-sm-8">
                                    @if (Model.Products?.Count > 0)
                                    {
                                        <span class="badge bg-danger">@Model.Products.Count sản phẩm</span>
                                        <small class="text-danger d-block mt-1">Không thể xóa danh mục đang có sản phẩm!</small>
                                    }
                                    else
                                    {
                                        <span class="badge bg-success">0 sản phẩm</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <form id="deleteForm" asp-action="Delete" method="post" class="mt-3">
                @Html.AntiForgeryToken()
                <input type="hidden" name="id" value="@Model.CategoryId" />
                
                @if (Model.Products?.Count > 0)
                {
                    <button type="button" class="btn btn-danger" disabled>
                        <i class="fas fa-trash"></i> Không thể xóa
                    </button>
                }
                else
                {
                    <button type="submit" class="btn btn-danger btn-delete">
                        <i class="fas fa-trash"></i> Xác nhận xóa
                    </button>
                }
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            // Xử lý nút xóa
            $('.btn-delete').click(function(e) {
                e.preventDefault();
                
                const form = $(this).closest('form');
                const categoryName = '@Model.Name';
                
                Swal.fire({
                    title: 'Xác nhận xóa?',
                    html: `
                        <div class="text-danger">
                            <p>Bạn có chắc chắn muốn xóa danh mục <strong>${categoryName}</strong>?</p>
                            <p class="mb-0">Hành động này không thể hoàn tác!</p>
                        </div>
                    `,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: '<i class="fas fa-trash"></i> Xóa',
                    cancelButtonText: '<i class="fas fa-times"></i> Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit(); // Submit form trực tiếp
                    }
                });
            });
        });
    </script>
} 