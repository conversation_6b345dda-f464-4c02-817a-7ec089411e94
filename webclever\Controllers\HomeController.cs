using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using webclever.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace webclever.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly WebcleverContext _context;

        public HomeController(ILogger<HomeController> logger, WebcleverContext context)
        {
            _logger = logger;
            _context = context;
        }

        [AllowAnonymous]
        public async Task<IActionResult> Index()
        {
            // Lấy danh sách category để hiển thị trong navigation và home page
            var categories = await _context.Categories
                .Include(c => c.Products)
                .ThenInclude(p => p.ProductVariants)
                .ToListAsync();

            ViewBag.Categories = categories;

            // Lấy sản phẩm cho từng category (hiển thị tất cả sản phẩm)
            var categoryProducts = new Dictionary<int, List<Product>>();
            foreach (var category in categories)
            {
                var products = await _context.Products
                    .Where(p => p.CategoryId == category.CategoryId)
                    .Include(p => p.ProductVariants)
                    .OrderByDescending(p => p.ProductId)
                    .ToListAsync();

                // Nếu có ít hơn 5 sản phẩm, duplicate để test carousel
                if (products.Count > 0 && products.Count < 8)
                {
                    var originalCount = products.Count;
                    var duplicates = new List<Product>();

                    for (int i = 0; i < (8 - originalCount); i++)
                    {
                        var sourceProduct = products[i % originalCount];
                        duplicates.Add(sourceProduct);
                    }

                    products.AddRange(duplicates);
                }

                categoryProducts[category.CategoryId] = products;
            }

            ViewBag.CategoryProducts = categoryProducts;

            return View(categories);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
