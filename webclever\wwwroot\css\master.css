body {
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* <PERSON><PERSON><PERSON> bảo trang tối thiểu bằng chiều cao màn hình */
    margin: 0 !important;
    padding: 0 !important;
    padding-top: 84px !important;
    background-color: #000000;
    font-family: Arial, sans-serif;
    overflow-x: hidden !important;
    width: 100vw !important;
    box-sizing: border-box !important;
}

/* Hide all scrollbars globally */
html {
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Hide webkit scrollbars */
*::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* Hide scrollbars for other browsers */
* {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
    box-sizing: border-box;
}

/* CSS Reset để loại bỏ margin/padding mặc định */
* {
    margin: 0;
    padding: 0;
}

/* Override Bootstrap container-fluid padding */
.container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: none !important;
    width: 100% !important;
}

/* Override tất cả container Bootstrap */
.container, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: none !important;
    width: 100% !important;
}

/* CSS Reset chỉ cho html và body */
html, body {
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background-color: #000000;
    color: white;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    min-height: 60px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}
.logo-image {
    height: 40px;
    width: 50px;
    margin-left: 10px;
    opacity: 0.95;
    transition: opacity 0.3s ease;
}

.logo-image:hover {
    opacity: 1;
}
.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: opacity 0.3s ease;
    flex-shrink: 0;
    margin-left: 60px;
}

.logo:hover {
    opacity: 0.8;
}
.logo-text {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    left: 0;
    margin-left: 250px;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    letter-spacing: -0.3px;
}

.clever {
    margin-right: 6px;
    color: #f5f5f7;
}

.technology {
    color: red;
    font-weight: 700;
}





.nav-container {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 30px;
    margin: 0;
}

.nav-links a {
    color: #f5f5f7;
    text-decoration: none;
    font-size: 17px;
    font-weight: 400;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

    .nav-links button {
        /* Remove these styles as they are no longer used for links */
        display: none; /* Hide the old button styles */
    }

.nav-icons {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-shrink: 0;
    margin-right: 250px;
}

.nav-icons button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.nav-icons button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-icons a {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.nav-icons a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

    .nav-icons img {
        width: 22px;
        height: 22px;
        opacity: 0.9;
        transition: opacity 0.3s ease;
    }

    .nav-icons img:hover {
        opacity: 1;
    }
.login-icon {
    color: #f5f5f7;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Main Content Placeholder */
.main-content {
    flex: 1;
    padding: 20px;
    text-align: center;
}

/* Footer */
.footer {
    background-color: #1d1d1f;
    color: white;
    padding: 40px 20px 20px 20px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.footer-brand {
    font-size: 28px;
    font-weight: bold;
}

.highlight {
    color: #ff2d55;
}

.footer-apple-logo {
    height: 28px;
    width: auto;
}

.footer-reseller {
    font-size: 13px;
    color: #86868b;
    margin-top: 5px;
}

.footer-sections {
    display: flex;
    justify-content: flex-start;
    gap: 80px;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-section {
    flex: 0 0 auto;
    min-width: 200px;
}

    .footer-section h3 {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #f5f5f7;
        letter-spacing: 0.5px;
    }

    .footer-section p {
        margin-bottom: 8px;
    }

    .footer-section p a {
        font-size: 13px;
        color: #86868b;
        text-decoration: none;
        line-height: 1.6;
        transition: color 0.3s ease;
    }

    .footer-section p a:hover {
        color: #f5f5f7;
    }

.social-icons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

    .social-icons img {
        width: 24px;
        height: 24px;
    }

.footer-bottom {
    font-size: 12px;
    color: #86868b;
    border-top: 1px solid #424245;
    padding-top: 20px;
    text-align: left;
    max-width: 1200px;
    margin: 0 auto;
    line-height: 1.6;
}

    .footer-bottom p {
        margin: 8px 0;
    }

    .footer-bottom a {
        color: #86868b;
        text-decoration: none;
    }

    .footer-bottom a:hover {
        color: #f5f5f7;
    }

.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.7);
    z-index: 9999;
    display: none;
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.search-bar {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 600px;
    max-width: 90vw;
    background: #222;
    border-radius: 30px;
    display: flex;
    align-items: center;
    padding: 10px 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.4);
}

    .search-bar input {
        flex: 1;
        background: none;
        border: none;
        outline: none;
        color: #fff;
        font-size: 1.2rem;
        padding: 10px;
    }

.close-overlay {
    background: none;
    border: none;
    color: #fff;
    font-size: 2rem;
    cursor: pointer;
    margin-left: 15px;
}
#openSearchBtn {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
}

/* Styling for navigation category links */
.nav-category-link {
    display: inline-block;
    padding: 10px 5px; /* Thêm padding để tạo kích thước cho ô */
    color: white; /* Chữ trắng */
    text-decoration: none; /* Bỏ gạch chân */
    border-radius: 5px; /* Bo góc nhẹ */
    margin: 0 10px;
    font-size: 16px;
    transition: background-color 0.3s, border-color 0.3s, color 0.3s; /* Hiệu ứng chuyển động mượt mà */
}

.nav-category-link:hover {
    background-color: #333; /* Đổi màu nền khi hover */
    border-color: #555; /* Đổi màu viền khi hover */
    color: white; /* Đảm bảo chữ vẫn trắng */
}

.nav-category-link:active {
    background-color: #555; /* Đổi màu nền khi click */
    border-color: #777; /* Đổi màu viền khi click */
    transform: translateY(1px); /* Hiệu ứng nhấn xuống */
}
