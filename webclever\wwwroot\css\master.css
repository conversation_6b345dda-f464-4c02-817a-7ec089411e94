﻿body {
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* <PERSON><PERSON><PERSON> bảo trang tối thiểu bằng chiều cao màn hình */
    margin: 0 !important;
    padding: 0 !important;
    background-color: #000000;
    font-family: Arial, sans-serif;
    overflow-x: hidden !important;
    width: 100vw !important;
    box-sizing: border-box !important;
}

/* Hide all scrollbars globally */
html {
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Hide webkit scrollbars */
*::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* Hide scrollbars for other browsers */
* {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
    box-sizing: border-box;
}

/* CSS Reset để loại bỏ margin/padding mặc định */
* {
    margin: 0;
    padding: 0;
}

/* Override Bootstrap container-fluid padding */
.container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: none !important;
    width: 100% !important;
}

/* Override tất cả container Bootstrap */
.container, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: none !important;
    width: 100% !important;
}

/* Loại bỏ tất cả margin và padding mặc định */
* {
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

/* Thêm lại padding cho content */
.content, main, .main-content {
    padding: 20px !important;
}

/* Thêm lại margin cho text elements */
p, h1, h2, h3, h4, h5, h6 {
    margin-bottom: 10px !important;
}

/* Thêm lại padding cho buttons */
.btn, button {
    padding: 8px 16px !important;
}

.navbar {
    display: flex;
    justify-content: space-between; /* Căn đều hai bên */
    align-items: center; /* Căn giữa dọc */
    padding: 10px 0 !important; /* Loại bỏ padding ngang hoàn toàn */
    background-color: #000000;
    color: white;
    flex-wrap: nowrap;
    width: 100vw !important; /* Sử dụng viewport width để tràn viền */
    box-sizing: border-box;
    margin: 0 !important;
    margin-left: calc(-50vw + 50%) !important;
    margin-right: calc(-50vw + 50%) !important;
    position: relative;
}
.logo-image {
    height: 50px; /* Match the height of the text */
    width: 60px;
    margin-right: 10px; /* Space between image and text */
}
.logo {
    display: flex;
    align-items: center; /* Căn giữa dọc */
    justify-content: center; /* Căn giữa ngang nếu cần */
    text-decoration: none; /* Ẩn gạch chân */
    padding-left: 20px !important; /* Thêm padding cho logo */
}
.logo-text {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    left: 0;
    font-size: 25px;
    font-weight: bold;
    color: #fff;
}

.clever  {
    margin-right: 5px; /* Điều chỉnh khoảng cách */
  
}
.technology {
    color: red;
}





.nav-container {
    display: flex;
    align-items: center; /* Căn giữa dọc */
    flex: 1;
    justify-content: space-between;
}

.nav-links {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

    .nav-links button {
        /* Remove these styles as they are no longer used for links */
        display: none; /* Hide the old button styles */
    }

.nav-icons {
    display: flex;
    gap: 15px;
    margin-left: 250px !important;
    padding-right: 20px !important; /* Thêm padding cho nav-icons */
}

    .nav-icons img {
        width: 24px;
        height: 24px;
    }
.login-icon {
    right: 200px;
    color: white;
    text-decoration-line: blink;
}

/* Main Content Placeholder */
.main-content {
    flex: 1;
    padding: 20px;
    text-align: center;
}

/* Footer */
.footer {
    background-color: #000000;
    color: white;
    padding: 20px !important;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding-top: 20px !important;
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    margin-right: calc(-50vw + 50%) !important;
    box-sizing: border-box;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-brand {
    font-size: 24px;
    font-weight: bold;
}

.highlight {
    color: #ff2d55;
}

.footer-apple-logo {
    height: 24px;
    width: auto;
}

.footer-reseller {
    font-size: 14px;
    color: #ccc;
}

.footer-sections {
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.footer-section {
    flex: 1;
}

    .footer-section h3 {
        font-size: 16px;
        margin-bottom: 10px;
        color: red;
    }

    .footer-section p a {
        font-size: 14px;
        margin-bottom: 5px;
        color: white;
        text-decoration: none;
    }

.social-icons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

    .social-icons img {
        width: 24px;
        height: 24px;
    }

.footer-bottom {
    font-size: 12px;
    color: #ccc;
    border-top: 1px solid #333;
    padding-top: 10px;
    text-align: center;
}

    .footer-bottom p {
        margin: 5px 0;
    }
    .footer-bottom a {
        margin: 5px 0;
    }

.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.7);
    z-index: 9999;
    display: none;
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.search-bar {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 600px;
    max-width: 90vw;
    background: #222;
    border-radius: 30px;
    display: flex;
    align-items: center;
    padding: 10px 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.4);
}

    .search-bar input {
        flex: 1;
        background: none;
        border: none;
        outline: none;
        color: #fff;
        font-size: 1.2rem;
        padding: 10px;
    }

.close-overlay {
    background: none;
    border: none;
    color: #fff;
    font-size: 2rem;
    cursor: pointer;
    margin-left: 15px;
}
#openSearchBtn {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
}

/* Styling for navigation category links */
.nav-category-link {
    display: inline-block;
    padding: 10px 5px; /* Thêm padding để tạo kích thước cho ô */
    color: white; /* Chữ trắng */
    text-decoration: none; /* Bỏ gạch chân */
    border-radius: 5px; /* Bo góc nhẹ */
    margin: 0 10px;
    font-size: 16px;
    transition: background-color 0.3s, border-color 0.3s, color 0.3s; /* Hiệu ứng chuyển động mượt mà */
}

.nav-category-link:hover {
    background-color: #333; /* Đổi màu nền khi hover */
    border-color: #555; /* Đổi màu viền khi hover */
    color: white; /* Đảm bảo chữ vẫn trắng */
}

.nav-category-link:active {
    background-color: #555; /* Đổi màu nền khi click */
    border-color: #777; /* Đổi màu viền khi click */
    transform: translateY(1px); /* Hiệu ứng nhấn xuống */
}
