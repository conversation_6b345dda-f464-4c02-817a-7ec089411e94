using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using webclever.Models;

namespace webclever.Controllers
{
    public class ProductsController : Controller
    {
        private readonly WebcleverContext _context;

        public ProductsController(WebcleverContext context)
        {
            _context = context;
        }

        // GET: Products (hiển thị tất cả sản phẩm)
        public async Task<IActionResult> Index(string sortBy = "featured")
        {
            IQueryable<Product> productsQuery = _context.Products
                .Include(p => p.ProductVariants)
                .Include(p => p.Category);

            // Apply sorting
            switch (sortBy)
            {
                case "price_asc":
                    productsQuery = productsQuery.OrderBy(p => p.ProductVariants.Min(pv => pv.Price));
                    break;
                case "price_desc":
                    productsQuery = productsQuery.OrderByDescending(p => p.ProductVariants.Min(pv => pv.Price));
                    break;
                case "newest":
                    productsQuery = productsQuery.OrderByDescending(p => p.CreatedAt);
                    break;
                case "featured":
                default:
                    productsQuery = productsQuery.OrderByDescending(p => p.ProductId);
                    break;
            }

            var products = await productsQuery.ToListAsync();
            ViewBag.SortBy = sortBy;

            return View(products);
        }

        // GET: Products/Category/5
        public async Task<IActionResult> Category(int? id, string series = "Tất cả", string sortBy = "featured")
        {
            if (id == null)
            {
                return NotFound();
            }

            var category = await _context.Categories
                .FirstOrDefaultAsync(c => c.CategoryId == id);

            if (category == null)
            {
                return NotFound();
            }

            IQueryable<Product> productsQuery = _context.Products
                .Where(p => p.CategoryId == id)
                .Include(p => p.ProductVariants);

            // Extract unique series from product names for sub-navigation
            var allProductNamesInCategories = await _context.Products
                .Where(p => p.CategoryId == id)
                .Select(p => p.Name)
                .Distinct()
                .ToListAsync();

            List<string> productSeries = new List<string> { "Tất cả" }; // "Tất cả" option
            foreach (var productName in allProductNamesInCategories)
            {
                // Example: Extract "iPhone 16" from "iPhone 16 Pro Max"
                Match match = Regex.Match(productName, @"^(\w+\s*\d+)");
                if (match.Success)
                {
                    string extractedSeries = match.Groups[1].Value.Trim();
                    if (!productSeries.Contains(extractedSeries))
                    {
                        productSeries.Add(extractedSeries);
                    }
                } else {
                    // Fallback: If no numerical series found, add the full name
                    if (!productSeries.Contains(productName))
                    {
                        productSeries.Add(productName);
                    }
                }
            }
            // Sort series alphabetically, but keep "Tất cả" at the beginning
            productSeries = productSeries.OrderBy(s => s == "Tất cả" ? "" : s).ToList();

            // Filter by series
            if (series != "Tất cả" && !string.IsNullOrEmpty(series))
            {
                productsQuery = productsQuery.Where(p => p.Name.Contains(series));
            }

            // Apply sorting
            switch (sortBy)
            {
                case "price_asc":
                    productsQuery = productsQuery.OrderBy(p => p.ProductVariants.Min(pv => pv.Price));
                    break;
                case "price_desc":
                    productsQuery = productsQuery.OrderByDescending(p => p.ProductVariants.Min(pv => pv.Price));
                    break;
                case "newest":
                    productsQuery = productsQuery.OrderByDescending(p => p.CreatedAt);
                    break;
                case "featured":
                default:
                    // Default sorting (e.g., by ProductId or some other relevance)
                    productsQuery = productsQuery.OrderByDescending(p => p.ProductId); // Example default sorting
                    break;
            }

            var products = await productsQuery.ToListAsync();

            ViewBag.Category = category;
            ViewBag.ProductSeries = productSeries;
            ViewBag.CurrentSeries = series;
            ViewBag.SortBy = sortBy;

            return View(products);
        }

        // GET: Products/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.ProductVariants)
                .FirstOrDefaultAsync(p => p.ProductId == id);

            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }
    }
}