@model webclever.Models.User

@{
    ViewData["Title"] = "Thông tin tài khoản";
}

<div class="container mt-5">
    <h2 class="mb-4 text-center text-white fw-bold">Thông tin tài khoản</h2>
    <div class="card shadow-sm mb-5">
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4 text-md-end">
                    <strong>Họ tên:</strong>
                </div>
                <div class="col-md-8">
                    @Model.FullName
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-4 text-md-end">
                    <strong>Email:</strong>
                </div>
                <div class="col-md-8">
                    @Model.Email
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-4 text-md-end">
                    <strong><PERSON><PERSON> điện thoại:</strong>
                </div>
                <div class="col-md-8">
                    @(string.IsNullOrEmpty(Model.Phone) ? "Chưa cập nhật" : Model.Phone)
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-4 text-md-end">
                    <strong>Địa chỉ:</strong>
                </div>
                <div class="col-md-8">
                    @(string.IsNullOrEmpty(Model.Address) ? "Chưa cập nhật" : Model.Address)
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-4 text-md-end">
                    <strong>Giới tính:</strong>
                </div>
                <div class="col-md-8">
                    @(Model.Gender == 0 ? "Nam" : Model.Gender == 1 ? "Nữ" : "Không xác định")
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-4 text-md-end">
                    <strong>Vai trò:</strong>
                </div>
                <div class="col-md-8">
                    @Model.Role
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-4 text-md-end">
                    <strong>Hạng khách hàng:</strong>
                </div>
                <div class="col-md-8">
                    @(Model.Rank != null ? Model.Rank.RankName : "Chưa có hạng")
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-4 text-md-end">
                    <strong>Ngày tạo tài khoản:</strong>
                </div>
                <div class="col-md-8">
                    @Model.CreatedAt?.ToString("dd/MM/yyyy HH:mm:ss")
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="#" class="btn btn-primary">Cập nhật thông tin</a>
            </div>
        </div>
    </div>
</div> 