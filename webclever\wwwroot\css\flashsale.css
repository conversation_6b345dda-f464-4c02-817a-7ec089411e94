﻿.flashsale-slider-container {
    display: flex;
    overflow-x: auto;
    gap: 15px;
    padding: 10px;
    background: #222;
}

.flashsale-slide-card {
    min-width: 180px;
    background: #fff;
    border-radius: 10px;
    padding: 10px;
    flex-shrink: 0;
}

.flashsale-product-card img {
    width: 100%;
    height: auto;
}

.product-info {
    margin-top: 8px;
}

.price {
    display: flex;
    flex-direction: column;
}

    .price .current {
        font-weight: bold;
        color: red;
    }

    .price .original {
        text-decoration: line-through;
        color: gray;
        font-size: 14px;
    }

    .price .discount {
        color: orange;
        font-weight: bold;
    }
.flashsale-header {
    background-color: #111;
    padding: 10px 20px;
    display: flex;
    justify-content: center; /* Căn giữa nội dung */
    border-bottom: 2px solid #333;
}

.flashsale-container {
    display: flex;
    align-items: center;
    gap: 30px; /* Khoảng cách giữa c<PERSON>c phần */
    flex-wrap: wrap;
}

.flashsale-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

    .flashsale-title img {
        height: 30px;
    }

    .flashsale-title span {
        font-size: 20px;
        font-weight: bold;
        color: white;
        text-transform: uppercase;
    }

.countdown-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

#countdown {
    font-size: 18px;
    background-color: #222;
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 4px;
}

.time-info {
    display: flex;
    gap: 20px;
    color: white;
    font-size: 14px;
}

    .time-info div {
        text-align: center;
    }
