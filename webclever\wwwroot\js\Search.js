﻿const openSearchBtn = document.getElementById('openSearchBtn');
const searchOverlay = document.getElementById('searchOverlay');
const closeSearchOverlay = document.getElementById('closeSearchOverlay');
const searchInputOverlay = document.getElementById('searchInputOverlay');

// Mở overlay
openSearchBtn.addEventListener('click', () => {
    searchOverlay.style.display = 'block';
    setTimeout(() => searchInputOverlay.focus(), 100);
    document.body.style.overflow = 'hidden';
});

// Đóng overlay
closeSearchOverlay.addEventListener('click', closeOverlay);
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') closeOverlay();
});

function closeOverlay() {
    searchOverlay.style.display = 'none';
    document.body.style.overflow = '';
    searchInputOverlay.value = '';
}

// G<PERSON>i request tìm kiếm
function submitSearch() {
    const keyword = searchInputOverlay.value.trim();
    if (keyword !== "") {
        window.location.href = `/Home/Results?keyword=${encodeURIComponent(keyword)}`;
    }
}
