@model webclever.Models.Product

@{
    ViewData["Title"] = "Xóa sản phẩm";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800"><PERSON><PERSON>a sản phẩm</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Bạn có chắc chắn muốn xóa sản phẩm này?</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center mb-4">
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid" style="max-height: 200px;" />
                        }
                        else
                        {
                            <img src="/images/no-image.png" alt="No image" class="img-fluid" style="max-height: 200px;" />
                        }
                    </div>
                </div>
                <div class="col-md-8">
                    <dl class="row">
                        <dt class="col-sm-3">Tên sản phẩm</dt>
                        <dd class="col-sm-9">@Model.Name</dd>

                        <dt class="col-sm-3">Thương hiệu</dt>
                        <dd class="col-sm-9">@Model.Brand</dd>

                        <dt class="col-sm-3">Danh mục</dt>
                        <dd class="col-sm-9">@Model.Category?.Name</dd>

                        <dt class="col-sm-3">Số biến thể</dt>
                        <dd class="col-sm-9">@(Model.ProductVariants?.Count ?? 0)</dd>
                    </dl>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Lưu ý:</strong> Việc xóa sản phẩm sẽ xóa tất cả các biến thể và hình ảnh liên quan. Hành động này không thể hoàn tác.
                    </div>

                    <form asp-action="Delete" class="mt-4">
                        @Html.AntiForgeryToken()
                        <input type="hidden" asp-for="ProductId" />
                        <button type="submit" class="btn btn-danger btn-delete">
                            <i class="fas fa-trash"></i> Xác nhận xóa
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 