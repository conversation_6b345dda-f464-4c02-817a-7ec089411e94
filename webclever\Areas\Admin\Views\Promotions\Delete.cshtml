@model webclever.Models.Promotion

@{
    ViewData["Title"] = "Xóa khuyến mãi";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Xóa khuyến mãi</h1>
        <div>
            <a asp-area="Admin" asp-controller="Promotions" asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="alert alert-danger">
        <h4 class="alert-heading">Cảnh báo!</h4>
        <p>Bạn có chắc chắn muốn xóa khuyến mãi này? Hành động này không thể hoàn tác.</p>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Thông tin khuyến mãi</h6>
        </div>
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-4">Tên khuyến mãi</dt>
                <dd class="col-sm-8">@Model.Title</dd>

                <dt class="col-sm-4">Mô tả</dt>
                <dd class="col-sm-8">@Model.Description</dd>

                <dt class="col-sm-4">Phần trăm giảm</dt>
                <dd class="col-sm-8">@(Model.DiscountPercent.HasValue ? string.Format("{0:N0}%", Model.DiscountPercent.Value) : "N/A")</dd>

                <dt class="col-sm-4">Ngày bắt đầu</dt>
                <dd class="col-sm-8">@(Model.StartDate.HasValue ? string.Format("{0:dd/MM/yyyy}", Model.StartDate.Value) : "N/A")</dd>

                <dt class="col-sm-4">Ngày kết thúc</dt>
                <dd class="col-sm-8">@(Model.EndDate.HasValue ? string.Format("{0:dd/MM/yyyy}", Model.EndDate.Value) : "N/A")</dd>

                <dt class="col-sm-4">Trạng thái</dt>
                <dd class="col-sm-8">
                    @{
                        var today = DateOnly.FromDateTime(DateTime.Now);
                    }
                    @if (Model.StartDate.HasValue && Model.EndDate.HasValue)
                    {
                        if (Model.StartDate.Value > today)
                        {
                            <span class="badge bg-secondary">Chưa bắt đầu</span>
                        }
                        else if (Model.EndDate.Value < today)
                        {
                            <span class="badge bg-danger">Đã kết thúc</span>
                        }
                        else
                        {
                            <span class="badge bg-success">Đang diễn ra</span>
                        }
                    }
                    else
                    {
                        <span class="badge bg-warning">Không xác định</span>
                    }
                </dd>

                <dt class="col-sm-4">Sản phẩm</dt>
                <dd class="col-sm-8">
                    @if (Model.Product != null)
                    {
                        <div class="d-flex align-items-center">
                            @if (!string.IsNullOrEmpty(Model.Product.ImageUrl))
                            {
                                <img src="@Model.Product.ImageUrl" alt="@Model.Product.Name" class="img-thumbnail me-2" style="width: 50px; height: 50px; object-fit: cover;">
                            }
                            <div>
                                <div>@Model.Product.Name</div>
                                @if (Model.Variant != null)
                                {
                                    <small class="text-muted">@Model.Variant.Color - @Model.Variant.Storage</small>
                                }
                            </div>
                        </div>
                    }
                    else
                    {
                        <span>N/A</span>
                    }
                </dd>
            </dl>
        </div>
    </div>

    <form asp-action="Delete" class="mt-4">
        <input type="hidden" asp-for="PromotionId" />
        <button type="submit" class="btn btn-danger">
            <i class="fas fa-trash"></i> Xóa
        </button>
    </form>
</div> 