@model webclever.Models.CustomerRank

@{
    ViewData["Title"] = "Xóa hạng khách hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Xóa hạng khách hàng</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin hạng khách hàng</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">ID</dt>
                        <dd class="col-sm-9">@Model.RankId</dd>

                        <dt class="col-sm-3">Tê<PERSON> hạng</dt>
                        <dd class="col-sm-9">@Model.RankName</dd>

                        <dt class="col-sm-3">Chi tiêu tối thiểu</dt>
                        <dd class="col-sm-9">@(Model.MinTotalSpend.HasValue ? string.Format("{0:N0} VNĐ", Model.MinTotalSpend.Value) : "N/A")</dd>

                        <dt class="col-sm-3">Tỷ lệ giảm giá</dt>
                        <dd class="col-sm-9">@(Model.DiscountRate.HasValue ? string.Format("{0:N1}%", Model.DiscountRate.Value) : "N/A")</dd>
                    </dl>

                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Cảnh báo</h5>
                        <p class="mb-0">Bạn có chắc chắn muốn xóa hạng khách hàng này? Hành động này không thể hoàn tác.</p>
                    </div>

                    @if (Model.Users != null && Model.Users.Any())
                    {
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-circle"></i> Không thể xóa</h5>
                            <p>Hạng khách hàng này đang được sử dụng bởi @Model.Users.Count khách hàng. Vui lòng chuyển các khách hàng sang hạng khác trước khi xóa.</p>
                        </div>
                    }
                    else
                    {
                        <form asp-action="Delete">
                            <input type="hidden" asp-for="RankId" />
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Xác nhận xóa
                            </button>
                        </form>
                    }
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin bổ sung</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Lưu ý</h5>
                        <ul class="mb-0">
                            <li>Không thể xóa hạng khách hàng đang được sử dụng</li>
                            <li>Khách hàng thuộc hạng này sẽ được chuyển về hạng mặc định</li>
                            <li>Hãy kiểm tra kỹ trước khi xóa</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 