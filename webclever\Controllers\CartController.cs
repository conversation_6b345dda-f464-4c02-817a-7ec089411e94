using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webclever.Models;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;

namespace webclever.Controllers
{
    public class CartController : Controller
    {
        private readonly WebcleverContext _context;

        public CartController(WebcleverContext context)
        {
            _context = context;
        }

        // GET: Cart
        public async Task<IActionResult> Index()
        {
            var userEmail = User.FindFirstValue(ClaimTypes.Email);
            
            if (string.IsNullOrEmpty(userEmail))
            {
                // Nếu chưa đăng nhập, hiển thị giỏ hàng trống
                return View(new List<Cart>());
            }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == userEmail);
            if (user == null)
            {
                return View(new List<Cart>());
            }

            var cartItems = await _context.Carts
                .Include(c => c.Variant)
                    .ThenInclude(v => v.Product)
                .Where(c => c.UserId == user.UserId)
                .ToListAsync();

            return View(cartItems);
        }

        // POST: Cart/AddToCart
        [HttpPost]
        public async Task<IActionResult> AddToCart(int variantId, int quantity = 1)
        {
            var userEmail = User.FindFirstValue(ClaimTypes.Email);
            
            if (string.IsNullOrEmpty(userEmail))
            {
                return Json(new { success = false, message = "Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng" });
            }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == userEmail);
            if (user == null)
            {
                return Json(new { success = false, message = "Không tìm thấy thông tin người dùng" });
            }

            // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
            var existingCartItem = await _context.Carts
                .FirstOrDefaultAsync(c => c.UserId == user.UserId && c.VariantId == variantId);

            if (existingCartItem != null)
            {
                // Nếu đã có, tăng số lượng
                existingCartItem.Quantity += quantity;
                _context.Update(existingCartItem);
            }
            else
            {
                // Nếu chưa có, thêm mới
                var cartItem = new Cart
                {
                    UserId = user.UserId,
                    VariantId = variantId,
                    Quantity = quantity,
                    AddedAt = DateTime.Now
                };
                _context.Carts.Add(cartItem);
            }

            await _context.SaveChangesAsync();
            return Json(new { success = true, message = "Đã thêm sản phẩm vào giỏ hàng" });
        }

        // POST: Cart/UpdateQuantity
        [HttpPost]
        public async Task<IActionResult> UpdateQuantity(int cartId, int quantity)
        {
            var cartItem = await _context.Carts.FindAsync(cartId);
            if (cartItem == null)
            {
                return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
            }

            if (quantity <= 0)
            {
                _context.Carts.Remove(cartItem);
            }
            else
            {
                cartItem.Quantity = quantity;
                _context.Update(cartItem);
            }

            await _context.SaveChangesAsync();
            return Json(new { success = true });
        }

        // POST: Cart/RemoveItem
        [HttpPost]
        public async Task<IActionResult> RemoveItem(int cartId)
        {
            var cartItem = await _context.Carts.FindAsync(cartId);
            if (cartItem == null)
            {
                return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
            }

            _context.Carts.Remove(cartItem);
            await _context.SaveChangesAsync();
            return Json(new { success = true });
        }

        // GET: Cart/GetCartCount
        public async Task<IActionResult> GetCartCount()
        {
            var userEmail = User.FindFirstValue(ClaimTypes.Email);
            
            if (string.IsNullOrEmpty(userEmail))
            {
                return Json(new { count = 0 });
            }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == userEmail);
            if (user == null)
            {
                return Json(new { count = 0 });
            }

            var count = await _context.Carts
                .Where(c => c.UserId == user.UserId)
                .SumAsync(c => c.Quantity ?? 0);

            return Json(new { count = count });
        }
    }
}
