using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webclever.Models;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;

namespace webclever.Controllers
{
    // Model for guest cart items stored in session
    public class GuestCartItem
    {
        public int VariantId { get; set; }
        public int Quantity { get; set; }
        public DateTime AddedAt { get; set; }
    }

    public class CartController : Controller
    {
        private readonly WebcleverContext _context;

        public CartController(WebcleverContext context)
        {
            _context = context;
        }

        // GET: Cart
        public async Task<IActionResult> Index()
        {
            var userEmail = User.FindFirstValue(ClaimTypes.Email);

            if (string.IsNullOrEmpty(userEmail))
            {
                // Khách vãng lai - lấy giỏ hàng từ session
                var guestCartItems = GetGuestCartItems();
                var cartItems = new List<Cart>();

                foreach (var guestItem in guestCartItems)
                {
                    var variant = await _context.ProductVariants
                        .Include(v => v.Product)
                        .FirstOrDefaultAsync(v => v.VariantId == guestItem.VariantId);

                    if (variant != null)
                    {
                        cartItems.Add(new Cart
                        {
                            CartId = guestItem.VariantId, // Sử dụng VariantId làm ID tạm
                            VariantId = guestItem.VariantId,
                            Quantity = guestItem.Quantity,
                            AddedAt = guestItem.AddedAt,
                            Variant = variant
                        });
                    }
                }

                return View(cartItems);
            }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == userEmail);
            if (user == null)
            {
                return View(new List<Cart>());
            }

            var userCartItems = await _context.Carts
                .Include(c => c.Variant)
                    .ThenInclude(v => v.Product)
                .Where(c => c.UserId == user.UserId)
                .ToListAsync();

            return View(userCartItems);
        }

        // POST: Cart/AddToCart
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddToCart(int variantId, int quantity = 1)
        {
            try
            {
                Console.WriteLine($"AddToCart called with variantId: {variantId}, quantity: {quantity}");

                var userEmail = User.FindFirstValue(ClaimTypes.Email);
                Console.WriteLine($"User email: {userEmail}");

                if (string.IsNullOrEmpty(userEmail))
                {
                    Console.WriteLine("Guest user - using session cart");
                    // Khách vãng lai - sử dụng session
                    var guestCartItems = GetGuestCartItems();
                    var existingItem = guestCartItems.FirstOrDefault(x => x.VariantId == variantId);

                    if (existingItem != null)
                    {
                        existingItem.Quantity += quantity;
                    }
                    else
                    {
                        guestCartItems.Add(new GuestCartItem
                        {
                            VariantId = variantId,
                            Quantity = quantity,
                            AddedAt = DateTime.Now
                        });
                    }

                    SaveGuestCartItems(guestCartItems);
                    Console.WriteLine("Product added to guest cart successfully");
                    return Json(new { success = true, message = "Đã thêm sản phẩm vào giỏ hàng" });
                }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == userEmail);
            if (user == null)
            {
                return Json(new { success = false, message = "Không tìm thấy thông tin người dùng" });
            }

            // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
            var existingCartItem = await _context.Carts
                .FirstOrDefaultAsync(c => c.UserId == user.UserId && c.VariantId == variantId);

            if (existingCartItem != null)
            {
                // Nếu đã có, tăng số lượng
                existingCartItem.Quantity += quantity;
                _context.Update(existingCartItem);
            }
            else
            {
                // Nếu chưa có, thêm mới
                var cartItem = new Cart
                {
                    UserId = user.UserId,
                    VariantId = variantId,
                    Quantity = quantity,
                    AddedAt = DateTime.Now
                };
                _context.Carts.Add(cartItem);
            }

            await _context.SaveChangesAsync();
            Console.WriteLine("Product added to cart successfully");
            return Json(new { success = true, message = "Đã thêm sản phẩm vào giỏ hàng" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding to cart: {ex.Message}");
                return Json(new { success = false, message = "Có lỗi xảy ra khi thêm vào giỏ hàng" });
            }
        }

        // POST: Cart/UpdateQuantity
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateQuantity(int cartId, int quantity)
        {
            var userEmail = User.FindFirstValue(ClaimTypes.Email);

            if (string.IsNullOrEmpty(userEmail))
            {
                // Khách vãng lai - cartId là VariantId
                var guestCartItems = GetGuestCartItems();
                var guestItem = guestCartItems.FirstOrDefault(x => x.VariantId == cartId);

                if (guestItem == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
                }

                if (quantity <= 0)
                {
                    guestCartItems.Remove(guestItem);
                }
                else
                {
                    guestItem.Quantity = quantity;
                }

                SaveGuestCartItems(guestCartItems);
                return Json(new { success = true });
            }

            // Người dùng đã đăng nhập
            var cartItem = await _context.Carts.FindAsync(cartId);
            if (cartItem == null)
            {
                return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
            }

            if (quantity <= 0)
            {
                _context.Carts.Remove(cartItem);
            }
            else
            {
                cartItem.Quantity = quantity;
                _context.Update(cartItem);
            }

            await _context.SaveChangesAsync();
            return Json(new { success = true });
        }

        // POST: Cart/RemoveItem
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RemoveItem(int cartId)
        {
            var userEmail = User.FindFirstValue(ClaimTypes.Email);

            if (string.IsNullOrEmpty(userEmail))
            {
                // Khách vãng lai - cartId là VariantId
                var guestCartItems = GetGuestCartItems();
                var guestItem = guestCartItems.FirstOrDefault(x => x.VariantId == cartId);

                if (guestItem == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
                }

                guestCartItems.Remove(guestItem);
                SaveGuestCartItems(guestCartItems);
                return Json(new { success = true });
            }

            // Người dùng đã đăng nhập
            var cartItem = await _context.Carts.FindAsync(cartId);
            if (cartItem == null)
            {
                return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
            }

            _context.Carts.Remove(cartItem);
            await _context.SaveChangesAsync();
            return Json(new { success = true });
        }

        // GET: Cart/GetCartCount
        public async Task<IActionResult> GetCartCount()
        {
            var userEmail = User.FindFirstValue(ClaimTypes.Email);

            if (string.IsNullOrEmpty(userEmail))
            {
                // Khách vãng lai - đếm từ session
                var guestCartItems = GetGuestCartItems();
                var count = guestCartItems.Sum(x => x.Quantity);
                return Json(new { count = count });
            }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == userEmail);
            if (user == null)
            {
                return Json(new { count = 0 });
            }

            var userCount = await _context.Carts
                .Where(c => c.UserId == user.UserId)
                .SumAsync(c => c.Quantity ?? 0);

            return Json(new { count = userCount });
        }

        // Helper methods for guest cart management
        private List<GuestCartItem> GetGuestCartItems()
        {
            var cartJson = HttpContext.Session.GetString("GuestCart");
            if (string.IsNullOrEmpty(cartJson))
            {
                return new List<GuestCartItem>();
            }
            return JsonConvert.DeserializeObject<List<GuestCartItem>>(cartJson) ?? new List<GuestCartItem>();
        }

        private void SaveGuestCartItems(List<GuestCartItem> items)
        {
            var cartJson = JsonConvert.SerializeObject(items);
            HttpContext.Session.SetString("GuestCart", cartJson);
        }
    }
}
