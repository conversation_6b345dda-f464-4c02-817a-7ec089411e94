﻿.product-container {
    display: flex;
    gap: 30px;
    margin: 20px;
    font-family: Arial, sans-serif;
}

.product-gallery {
    flex: 1;
    max-width: 50%;
}

#main-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    border: 1px solid #ccc;
}

.thumbnail-container {
    margin-top: 10px;
    display: flex;
    gap: 10px;
}

.thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border: 2px solid transparent;
    cursor: pointer;
    border-radius: 6px;
    transition: border-color 0.3s ease;
}

    .thumbnail.active,
    .thumbnail:hover {
        border-color: #007bff;
    }

.product-details {
    flex: 1;
}

    .product-details h1 {
        font-size: 24px;
        margin-bottom: 10px;
    }

.price {
    font-size: 22px;
    font-weight: bold;
    color: #e60000;
    margin-bottom: 10px;
}

.original-price {
    text-decoration: line-through;
    font-size: 14px;
    color: #777;
    margin-left: 10px;
}

.variants {
    margin: 20px 0;
}

    .variants label {
        display: block;
        margin-top: 10px;
        font-weight: bold;
    }

    .variants button {
        margin: 5px 5px 0 0;
        padding: 6px 12px;
        border: 1px solid #ccc;
        background-color: #fff;
        cursor: pointer;
        border-radius: 4px;
        transition: background 0.3s;
    }

        .variants button:hover {
            background-color: #f0f0f0;
        }

.color-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid #ccc;
}

.promo ul {
    padding-left: 20px;
    color: #007b00;
}

.buy-now {
    background-color: #ff4d00;
    color: white;
    padding: 10px 20px;
    font-size: 16px;
    margin-top: 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

    .buy-now:hover {
        background-color: #cc3e00;
    }

.installments button {
    margin: 10px 10px 0 0;
    padding: 8px 16px;
    border: 1px solid #007bff;
    background-color: white;
    color: #007bff;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
}

    .installments button:hover {
        background-color: #007bff;
        color: white;
    }

.product-details table {
    width: 100%;
    margin-top: 20px;
    border-collapse: collapse;
}

.product-details td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
}
