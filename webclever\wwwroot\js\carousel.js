﻿document.addEventListener('DOMContentLoaded', function () {
    const carousel = document.getElementById('carousel');
    const leftArrow = document.getElementById('leftArrow');
    const rightArrow = document.getElementById('rightArrow');

    let scrollAmount = 0;
    const scrollPerClick = 320; // Chiều rộng ước lượng của mỗi item + margin

    leftArrow.addEventListener('click', function () {
        carousel.scrollBy({
            left: -scrollPerClick,
            behavior: 'smooth'
        });
    });

    rightArrow.addEventListener('click', function () {
        carousel.scrollBy({
            left: scrollPerClick,
            behavior: 'smooth'
        });
    });
});
