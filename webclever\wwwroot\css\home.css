﻿
.home-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    max-width: 1200px;
    gap: 16px;
    margin: auto; /* <PERSON><PERSON>n gi<PERSON>a */
    color: white;
    padding: 40px 20px;
}

.home-product-card {
    background-color: #2c2c2e;
    border-radius: 12px;
    overflow: hidden;
    width: 180px;
    height: 200px;
    text-align: center;
    transition: transform 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

    .home-product-card:hover {
        transform: scale(1.03);
        text-decoration: none;
        color: white;
    }

.home-product-image {
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
}

    .home-product-image img {
        max-height: 100%;
        max-width: 100%;
    }

.home-product-title {
    margin-top: auto;
    margin-bottom: 15px;
    font-weight: 500;
}

/* Category Sections */
.category-section {
    margin: 40px 0;
    width: 100%;
}

.category-container {
    position: relative;
    height: 400px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.category-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
    color: white;
    position: relative;
}

.category-link:hover {
    text-decoration: none;
    color: white;
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.6) 100%);
    transition: background 0.3s ease;
}

.category-container:hover .category-overlay {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.05) 50%, rgba(0, 0, 0, 0.5) 100%);
}

.category-header {
    position: absolute;
    top: 50%;
    left: 50px;
    transform: translateY(-50%);
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 20px;
}

.category-header h1 {
    font-size: 48px;
    font-weight: 700;
    color: white;
    margin: 0;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
    transition: transform 0.3s ease;
}

.category-container:hover .category-header h1 {
    transform: scale(1.05);
}

.apple-logo {
    width: 60px;
    height: 60px;
    filter: brightness(0) invert(1);
    transition: transform 0.3s ease;
}

.category-container:hover .apple-logo {
    transform: scale(1.1) rotate(5deg);
}

/* Responsive */
@media (max-width: 768px) {
    .category-container {
        height: 250px;
    }

    .category-header {
        left: 30px;
    }

    .category-header h1 {
        font-size: 32px;
    }

    .apple-logo {
        width: 40px;
        height: 40px;
    }
}

/* Clean Carousel Styles - No Background */
.carousel-container {
    position: relative;
    width: 100%;
    max-width: 1320px;
    margin: 0 auto 80px auto;
    padding: 20px 60px;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.carousel-wrapper {
    overflow: hidden !important;
    width: 1200px;
    max-width: 100%;
    background: transparent;
    position: relative;
    margin: 0 auto;
}

/* Hide all scrollbars completely */
.carousel-wrapper::-webkit-scrollbar,
.carousel-container::-webkit-scrollbar,
.carousel-track::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

.carousel-wrapper,
.carousel-container,
.carousel-track {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
}

/* Hide any horizontal scrollbars */
* {
    overflow-x: hidden !important;
}

/* Force hide all scrollbars globally */
html, body {
    overflow-x: hidden !important;
}

/* Hide scrollbars on all carousel elements */
.carousel-container,
.carousel-wrapper,
.carousel-track,
.iphone-container {
    overflow: hidden !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
}

/* Remove any potential scrollbars from main containers */
main, .container-fluid, .pb-3 {
    overflow-x: hidden !important;
}

.carousel-track {
    display: flex;
    gap: 20px;
    transition: transform 0.3s ease;
    background: transparent;
    width: max-content;
}

.carousel-card {
    flex: 0 0 285px;
    width: 285px;
    background: #2c2c2e;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
    border: 1px solid #404040;
}

.carousel-card:hover {
    transform: translateY(-5px);
}

.carousel-image {
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    background: #1c1c1e;
    border-radius: 10px;
    overflow: hidden;
}

.carousel-image img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
}

.carousel-info {
    color: white;
}

.carousel-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 12px 0;
    color: #fff;
    line-height: 1.3;
    height: 2.6em;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-pricing {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.current-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #fff;
}

.original-price {
    font-size: 0.9rem;
    color: #888;
    text-decoration: line-through;
}

.discount {
    background: #ff3b30;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.online-price {
    font-size: 0.85rem;
    color: #ff9500;
    font-weight: 500;
    margin-top: 5px;
}

/* Arrows positioned below products */
.arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #007AFF;
    color: #007AFF;
    font-size: 20px;
    font-weight: bold;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.arrow:hover {
    background: #007AFF;
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 122, 255, 0.5);
}

.arrow.left {
    left: -25px;
}

.arrow.right {
    right: -25px;
}

/* Hide Indicators */
.indicator {
    display: none;
}

/* Ensure no gray backgrounds anywhere */
.container-fluid, .main, main {
    background: transparent !important;
}

/* Remove any potential gray backgrounds from parent containers */
.pb-3 {
    background: transparent !important;
}

/* iPhone Container Styles */
.iphone-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0 20px 0;
    padding: 0 20px;
}

.iphone-link {
    text-decoration: none;
    color: inherit;
    transition: transform 0.3s ease;
}

.iphone-link:hover {
    transform: scale(1.05);
    text-decoration: none;
    color: inherit;
}

.iphone-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    background: transparent;
    padding: 20px 40px;
    border: none;
    box-shadow: none;
}

.iphone-header h1 {
    color: #fff;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.apple-logo {
    width: 50px;
    height: 50px;
    filter: brightness(0) invert(1);
}

/* Simplified Responsive Design */
@media (max-width: 1024px) {
    .carousel-container {
        max-width: 920px;
        padding: 20px 50px;
    }

    .carousel-wrapper {
        width: 900px;
    }

    .carousel-card {
        flex: 0 0 280px;
        width: 280px;
    }
}

@media (max-width: 768px) {
    .carousel-container {
        max-width: 580px;
        padding: 20px 50px;
    }

    .carousel-wrapper {
        width: 560px;
    }

    .carousel-card {
        flex: 0 0 260px;
        width: 260px;
        padding: 18px;
    }

    .carousel-image {
        height: 180px;
    }

    .arrow {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .arrow.left {
        left: calc(50% - 50px);
    }

    .arrow.right {
        right: calc(50% - 50px);
    }

    .iphone-header {
        padding: 15px 25px;
        gap: 10px;
    }

    .iphone-header h1 {
        font-size: 1.8rem;
    }

    .apple-logo {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .carousel-container {
        max-width: 300px;
        padding: 15px 40px;
    }

    .carousel-wrapper {
        width: 280px;
    }

    .carousel-card {
        flex: 0 0 240px;
        width: 240px;
        padding: 15px;
    }

    .carousel-image {
        height: 160px;
    }

    .carousel-title {
        font-size: 0.95rem;
        height: 2.2em;
    }

    .current-price {
        font-size: 1.1rem;
    }

    .arrow {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }

    .arrow.left {
        left: calc(50% - 40px);
    }

    .arrow.right {
        right: calc(50% - 40px);
    }

    .iphone-header h1 {
        font-size: 1.5rem;
    }

    .apple-logo {
        width: 30px;
        height: 30px;
    }
}
