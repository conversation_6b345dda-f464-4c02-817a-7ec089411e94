:root {
    --primary-color: #e71d1d;
    --secondary-color: #00FFFF;
    --dark-color: #222;
    --light-color: #fff;
    --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease-in-out;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
}

.banner-container {
    width: 100%;
    height: 500px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow);
    background-color: #000;
    margin-top: 84px;
    z-index: 1;
}

.arrow-buttons {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    z-index: 5;
    transform: translateY(-50%);
    pointer-events: none;
}

.arrow-btn {
    background-color: rgba(85, 85, 85, 0.6);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    pointer-events: auto;
}

    .arrow-btn.prev {
        margin-left: 20px;
    }

    .arrow-btn.next {
        margin-right: 20px;
    }

    .arrow-btn:hover {
        background-color: rgba(100, 100, 100, 0.9);
    }

    .arrow-btn.prev::before {
        content: "‹";
        font-size: 26px;
        font-weight: 300;
        line-height: 1;
        margin-bottom: 3px;
    }

    .arrow-btn.next::before {
        content: "›";
        font-size: 26px;
        font-weight: 300;
        line-height: 1;
        margin-bottom: 3px;
    }

/* Responsive styles */
@media (max-width: 768px) {
    .arrow-btn {
        width: 30px;
        height: 30px;
    }

        .arrow-btn.prev {
            margin-left: 10px;
        }

        .arrow-btn.next {
            margin-right: 10px;
        }

            .arrow-btn.prev::before,
            .arrow-btn.next::before {
                font-size: 22px;
            }
}


.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
    background-color: rgba(255, 255, 255, 0.7);
    color: var(--dark-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition);
}

    .nav-btn:hover {
        background-color: rgba(255, 255, 255, 0.9);
    }


/* Slides */

.slides-container {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    overflow: hidden;
}

.slide {
    width: 100%;
    height: 100%;
    position: absolute;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.8s ease-in-out;
    z-index: 1;
}

    .slide.active {
        opacity: 1;
        transform: translateX(0);
        z-index: 2;
    }

    .slide.prev {
        transform: translateX(-100%);
    }

    .slide.next {
        transform: translateX(100%);
    }

.slide-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
    color: inherit;
}

    .slide-link:hover {
        text-decoration: none;
        color: inherit;
    }

.slide-content {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.slide-text {
    position: absolute;
    top: 50%;
    left: 50px;
    transform: translateY(-50%) translateX(-30px);
    z-index: 3;
    background: rgba(0, 0, 0, 0.6);
    padding: 30px;
    border-radius: 15px;
    max-width: 500px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: all 0.8s ease-in-out;
}

.slide.active .slide-text {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
}

.slide-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

    .slide-image::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.3) 100%);
        pointer-events: none;
        z-index: 1;
    }

    .slide-image img {
        width: 100%;
        height: 120%;
        object-fit: contain;
        object-position: center;
        display: block;
        transition: all 0.8s ease-in-out;
        background-color: #000;
        margin-top: -40px;
        transform: scale(1);
        opacity: 1;
    }

    .slide:not(.active) .slide-image img {
        transform: scale(0.9);
        opacity: 0.7;
    }

    .slide.active .slide-image img {
        transform: scale(1);
        opacity: 1;
    }

    .slide-link:hover .slide-image img {
        transform: scale(1.05);
    }

    .slide:hover .slide-image img {
        transform: scale(1.05);
    }
/* Headings & button */

.slide-text h2 {
    font-size: 36px;
    font-weight: 800;
    color: var(--light-color);
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.slide-text h3 {
    font-size: 24px;
    font-weight: 600;
    color: var(--light-color);
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.promo-button {
    background-color: var(--primary-color);
    color: var(--light-color);
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
    display: inline-block;
    margin-bottom: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.slide-text p {
    font-size: 16px;
    color: var(--light-color);
    margin-bottom: 8px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}
/* Dots indicator */

.dots-container {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
}

    .dot.active {
        background-color: var(--light-color);
        transform: scale(1.2);
    }
/* Responsive */

@media (max-width: 768px) {
    .banner-container {
        height: 350px;
    }

    .slide-text {
        left: 20px;
        right: 20px;
        max-width: calc(100% - 40px);
        padding: 20px;
    }

    .slide-text h2 {
        font-size: 24px;
    }

    .slide-text h3 {
        font-size: 18px;
    }

    .slide-text p {
        font-size: 14px;
    }

    .promo-button {
        font-size: 12px;
        padding: 8px 16px;
    }
}
