using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webclever.Models;

namespace webclever.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class ReviewsController : Controller
    {
        private readonly WebcleverContext _context;

        public ReviewsController(WebcleverContext context)
        {
            _context = context;
        }

        // GET: Admin/Reviews
        public async Task<IActionResult> Index()
        {
            var reviews = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Variant)
                    .ThenInclude(pv => pv.Product)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
            return View(reviews);
        }

        // GET: Admin/Reviews/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var review = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Variant)
                    .ThenInclude(v => v.Product)
                .FirstOrDefaultAsync(m => m.ReviewId == id);

            if (review?.User == null || review?.Variant == null)
            {
                return NotFound();
            }

            return View(review);
        }

        // GET: Admin/Reviews/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var review = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Variant)
                    .ThenInclude(v => v.Product)
                .FirstOrDefaultAsync(m => m.ReviewId == id);

            if (review?.User == null || review?.Variant == null)
            {
                return NotFound();
            }

            return View(review);
        }

        // POST: Admin/Reviews/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var review = await _context.Reviews.FindAsync(id);
            if (review != null)
            {
                _context.Reviews.Remove(review);
                await _context.SaveChangesAsync();

                // Cập nhật lại đánh giá trung bình của sản phẩm
                var productVariant = await _context.ProductVariants
                    .Include(pv => pv.Reviews)
                    .FirstOrDefaultAsync(pv => pv.VariantId == review.VariantId);

                if (productVariant != null)
                {
                    var averageRating = productVariant.Reviews.Any() 
                        ? productVariant.Reviews.Average(r => r.Rating) 
                        : 0;
                }
            }
            return RedirectToAction(nameof(Index));
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var review = await _context.Reviews
                .Include(r => r.User)
                .Include(r => r.Variant)
                    .ThenInclude(v => v.Product)
                .FirstOrDefaultAsync(m => m.ReviewId == id);

            if (review?.User == null || review?.Variant == null)
            {
                return NotFound();
            }

            return View(review);
        }

        private bool ReviewExists(int id)
        {
            return _context.Reviews.Any(e => e.ReviewId == id);
        }
    }
} 