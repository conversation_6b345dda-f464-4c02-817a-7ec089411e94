﻿.cartitem-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.cartitem-backlink {
    display: inline-block;
    margin-bottom: 15px;
    color: #00bfff; 
    text-decoration: none; 
}

.cartitem-title h3 {
    color: #fff;
}

.cartitem-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.cartitem-row {
    display: flex;
    gap: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.cartitem-col {
    flex: 1;
}

.cartitem-img-wrap {
    flex: 0 0 100px;
}

.cartitem-img {
    width: 100%;
    border-radius: 5px;
    object-fit: cover;
}

.cartitem-product-name {
    font-weight: bold;
    font-size: 16px;
}

.cartitem-price,
.cartitem-total {
    color: #c62828;
    font-weight: bold;
}

.cartitem-qty-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.cartitem-qty-input {
    width: 50px;
    text-align: center;
}

.cartitem-btn {
    background: #ddd;
    border: none;
    padding: 5px 10px;
    cursor: pointer;
}

.cartitem-remove {
    color: red;
    font-size: 0.9rem;
    text-decoration: none;
}

.cartitem-form {
    margin-top: 30px;
}

.cartitem-form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.cartitem-form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.cartitem-form-group-full {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.cartitem-address-select {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.cartitem-address-detail {
    margin-top: 5px;
}

.total-label {
    font-weight: bold;
}

.total-amount {
    font-size: 20px;
    color: #d32f2f;
    margin-top: 5px;
}

.cartitem-policy-check {
    margin-bottom: 15px;
}

.cartitem-submit-wrap {
    margin-top: 10px;
}

.cartitem-submit {
    background: #007bff;
    border: none;
    padding: 10px;
    color: white;
    width: 100%;
    border-radius: 5px;
    font-size: 16px;
}
