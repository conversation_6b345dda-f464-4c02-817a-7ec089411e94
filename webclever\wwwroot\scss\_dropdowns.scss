// Custom Dropdown Styling

.dropdown {
  .dropdown-menu {
    font-size: $dropdown-font-size;
    .dropdown-header {
      @extend .text-uppercase;
      font-weight: 800;
      font-size: 0.65rem;
      color: white;
    }
  }
}

// Utility class to hide arrow from dropdown

.dropdown.no-arrow {
  .dropdown-toggle::after {
    display: none;
  }
}

// Custom styling for login icon dropdown toggle
.dropdown-toggle.login-icon {
    color: $white;
    white-space: nowrap;
}

// Ẩn mũi tên cho biểu tượng người dùng
.login-icon.dropdown-toggle::after {
  display: none !important; // Đ<PERSON><PERSON> bảo ẩn hoàn toàn mũi tên
}
