@model webclever.Models.Product

@{
    ViewData["Title"] = "Thêm sản phẩm mới";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Thêm sản phẩm mới</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            @if (ViewData["VariantImageStatus"] != null)
            {
                <div class="alert alert-info" role="alert">
                    @ViewData["VariantImageStatus"]
                </div>
            }
            @if (ViewData["VariantImageUploadStatus"] != null)
            {
                <div class="alert alert-success" role="alert">
                    @ViewData["VariantImageUploadStatus"]
                </div>
            }
            @if (ViewData["VariantImageUploadError"] != null)
            {
                <div class="alert alert-danger" role="alert">
                    @ViewData["VariantImageUploadError"]
                </div>
            }
            @if (ViewData["RequestInfo"] != null)
            {
                <div class="alert alert-info" role="alert">
                    <strong>Request Info:</strong> @ViewData["RequestInfo"]
                </div>
            }
            @if (ViewData["FormData"] != null)
            {
                <div class="alert alert-info" role="alert">
                    <strong>Form Data:</strong> @ViewData["FormData"]
                </div>
            }
            @if (ViewData["FilesInfo"] != null)
            {
                <div class="alert alert-info" role="alert">
                    <strong>Files Info:</strong> @ViewData["FilesInfo"]
                </div>
            }
            <form asp-action="Create" enctype="multipart/form-data" class="needs-validation" novalidate data-form-type="product-ajax-form">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <label asp-for="Name" class="control-label">Tên sản phẩm</label>
                            <input asp-for="Name" class="form-control" required />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Brand" class="control-label">Thương hiệu</label>
                            <input asp-for="Brand" class="form-control" required />
                            <span asp-validation-for="Brand" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="CategoryId" class="control-label">Danh mục</label>
                            <select asp-for="CategoryId" class="form-control" asp-items="ViewBag.CategoryId" required>
                                <option value="">-- Chọn danh mục --</option>
                            </select>
                            <span asp-validation-for="CategoryId" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Description" class="control-label">Mô tả</label>
                            <textarea asp-for="Description" class="form-control" rows="4"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label class="control-label">Hình ảnh sản phẩm</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="imageFile" name="imageFile" accept="image/*">
                                <label class="custom-file-label" for="imageFile">Chọn file...</label>
                            </div>
                            <div class="mt-2">
                                <img id="imagePreview" src="/images/no-image.png" alt="Preview" class="img-thumbnail" style="max-width: 200px;" />
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="my-4" />

                <div class="mb-3">
                    <h5>Biến thể sản phẩm</h5>
                    <button type="button" id="addVariant" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> Thêm biến thể
                    </button>
                </div>

                <div id="variantsContainer">
                    <!-- Biến thể sẽ được thêm vào đây bằng JavaScript -->
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu sản phẩm
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 
