@model webclever.Models.User

@{
    ViewData["Title"] = "Chi tiết người dùng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chi tiết người dùng</h1>
        <div>
            <a asp-action="Edit" asp-route-id="@Model.UserId" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
            </a>
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin người dùng</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">ID</dt>
                        <dd class="col-sm-9">@Model.UserId</dd>

                        <dt class="col-sm-3">Họ tên</dt>
                        <dd class="col-sm-9">@Model.FullName</dd>

                        <dt class="col-sm-3">Email</dt>
                        <dd class="col-sm-9">@Model.Email</dd>

                        <dt class="col-sm-3">Số điện thoại</dt>
                        <dd class="col-sm-9">@Model.Phone</dd>

                        <dt class="col-sm-3">Địa chỉ</dt>
                        <dd class="col-sm-9">@Model.Address</dd>

                        <dt class="col-sm-3">Hạng khách hàng</dt>
                        <dd class="col-sm-9">
                            <span class="badge bg-info">@Model.Rank?.RankName</span>
                        </dd>

                        <dt class="col-sm-3">Ngày tham gia</dt>
                        <dd class="col-sm-9">@(Model.CreatedAt.HasValue ? string.Format("{0:dd/MM/yyyy}", Model.CreatedAt.Value) : "N/A")</dd>
                    </dl>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Danh sách đơn hàng</h6>
                </div>
                <div class="card-body">
                    @if (Model.Orders != null && Model.Orders.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered" id="ordersTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Mã đơn hàng</th>
                                        <th>Ngày đặt</th>
                                        <th>Tổng tiền</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var order in Model.Orders)
                                    {
                                        <tr>
                                            <td>@order.OrderId</td>
                                            <td>@(order.OrderDate.HasValue ? string.Format("{0:dd/MM/yyyy}", order.OrderDate.Value) : "N/A")</td>
                                            <td>@(order.TotalAmount.HasValue ? string.Format("{0:N0} đ", order.TotalAmount.Value) : "N/A")</td>
                                            <td>
                                                <span class="badge bg-@(order.OrderStatus switch {
                                                    "Pending" => "warning",
                                                    "Processing" => "info",
                                                    "Completed" => "success",
                                                    "Cancelled" => "danger",
                                                    _ => "secondary"
                                                })">
                                                    @order.OrderStatus
                                                </span>
                                            </td>
                                            <td>
                                                <a asp-controller="Orders" asp-action="Details" asp-route-id="@order.OrderId" class="btn btn-info btn-sm">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Chưa có đơn hàng nào
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin bổ sung</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Lưu ý</h5>
                        <ul class="mb-0">
                            <li>Người dùng có thể có nhiều đơn hàng</li>
                            <li>Hạng khách hàng được cập nhật tự động</li>
                            <li>Có thể chỉnh sửa thông tin người dùng</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#ordersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[1, 'desc']]
            });
        });
    </script>
} 