@model IEnumerable<webclever.Models.CustomerRank>

@{
    ViewData["Title"] = "Danh sách hạng khách hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Danh sách hạng khách hàng</h1>
        <a asp-area="Admin" asp-controller="Ranks" asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm mới
        </a>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tên hạng</th>
                            <th>Chi tiêu tối thiểu</th>
                            <th>Tỷ lệ giảm giá</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.RankId</td>
                                <td>@item.RankName</td>
                                <td>@item.MinTotalSpend?.ToString("N0") VNĐ</td>
                                <td>@item.DiscountRate?.ToString("N1")%</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-area="Admin" asp-controller="Ranks" asp-action="Edit" asp-route-id="@item.RankId" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Ranks" asp-action="Details" asp-route-id="@item.RankId" class="btn btn-info btn-sm">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Ranks" asp-action="Delete" asp-route-id="@item.RankId" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#dataTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[2, 'asc']], // Sắp xếp theo chi tiêu tối thiểu
                columnDefs: [
                    { orderable: false, targets: [4] } // Không cho phép sắp xếp cột thao tác
                ]
            });
        });
    </script>
} 