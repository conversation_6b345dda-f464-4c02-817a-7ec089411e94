@model webclever.Models.Banner

@{
    ViewData["Title"] = "Chi tiết banner";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chi tiết banner</h1>
        <div>
            <a asp-action="Edit" asp-route-id="@Model.BannerId" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
            </a>
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin banner</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">ID</dt>
                                <dd class="col-sm-8">@Model.BannerId</dd>

                                <dt class="col-sm-4">Tiêu đề</dt>
                                <dd class="col-sm-8">@Model.Title</dd>

                                <dt class="col-sm-4">Link</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.Link))
                                    {
                                        <a href="@Model.Link" target="_blank" class="text-primary">
                                            <i class="fas fa-external-link-alt"></i> Xem link
                                        </a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Không có link</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Thứ tự</dt>
                                <dd class="col-sm-8">@Model.DisplayOrder</dd>

                                <dt class="col-sm-4">Trạng thái</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive == true)
                                    {
                                        <span class="badge bg-success">Đang hiển thị</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">Đã ẩn</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                                {
                                    <img src="@Model.ImageUrl" alt="@Model.Title" class="img-fluid rounded" style="max-height: 300px;" />
                                }
                                else
                                {
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> Không có hình ảnh
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin bổ sung</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Lưu ý</h5>
                        <ul class="mb-0">
                            <li>Banner sẽ được hiển thị theo thứ tự từ nhỏ đến lớn</li>
                            <li>Chỉ banner đang hiển thị mới xuất hiện trên trang chủ</li>
                            <li>Hình ảnh banner nên có kích thước đồng nhất</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 