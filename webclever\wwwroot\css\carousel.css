.iphone-container {
    margin: 40px 0 20px;
    text-align: center;
}

.iphone-link {
    text-decoration: none;
}

.iphone-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.apple-logo {
    width: 18px;
    height: 18px;
    object-fit: contain;
    filter: brightness(0) invert(1); /* Giúp logo trắng khi nền tối */
}

.iphone-header h1 {
    font-size: 20px;
    font-weight: 600;
    color: white;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}


.carousel-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 20px 0;
    background-color: #3d3d3f; /* hoặc màu nền bạn dùng */
    overflow: hidden;
}

.carousel-wrapper {
    overflow: hidden;
    width: 1260px; /* 1240 + 20, đ<PERSON> dư 1 margin */
    margin: 0 auto;
    padding: 0;
}

@media (max-width: 1200px) {
    .carousel-wrapper {
        width: 930px; /* 3 card */
    }
}
@media (max-width: 992px) {
    .carousel-wrapper {
        width: 620px; /* 2 card */
    }
}
@media (max-width: 650px) {
    .carousel-wrapper {
        width: 300px; /* 1 card */
    }
}

.carousel-track {
    display: flex;
    transition: transform 0.3s ease;
}

.carousel-card {
    min-width: 285px;
    max-width: 285px;
    margin-right: 20px;
    background: #2c2c2e;
    border-radius: 28px; /* Bo tròn góc nhiều hơn */
    color: #fff;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px 16px 24px 16px;
    transition: box-shadow 0.2s;
}

.carousel-card:last-child {
    margin-right: 0;
}

.carousel-card:hover {
    box-shadow: 0 12px 40px rgba(0,0,0,0.25);
}

.carousel-image {
    width: 180px;
    height: 180px;
    background: #232325;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 18px;
    overflow: hidden;
}

.carousel-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 16px;
    background: #232325;
    display: block;
    margin: 0 auto;
}

.carousel-title {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 12px;
    min-height: 48px; /* Đảm bảo đều hàng */
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-pricing {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 8px;
}

.current-price {
    font-size: 22px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 2px;
}

.original-price {
    font-size: 15px;
    color: #bbb;
    text-decoration: line-through;
    margin-bottom: 2px;
}

.discount {
    font-size: 14px;
    color: #ff4747;
    font-weight: bold;
    background: #fff;
    border-radius: 8px;
    padding: 2px 8px;
    margin-bottom: 2px;
}

.online-price {
    color: #ffb300;
    font-size: 15px;
    font-weight: 500;
    margin-top: 4px;
}

.card {
    background-color: #2c2c2e;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    color: #fff;
}

.product-img {
    max-width: 100%;
    height: auto;
    margin-bottom: 12px;
}

.card-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.product-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
}

.price {
    display: flex;
    gap: 6px;
    align-items: center;
}

    .price .current {
        color: #fff;
        font-weight: bold;
    }

    .price .original {
        text-decoration: line-through;
        color: #888;
        font-size: 14px;
    }

    .price .discount {
        color: #f90;
        font-size: 14px;
        font-weight: bold;
    }

/* Nút mũi tên trái/phải */
.arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    font-size: 24px;
    color: #fff;
    cursor: pointer;
    z-index: 1;
    padding: 0 12px;
}

    .arrow.left {
        left: 0;
    }

    .arrow.right {
        right: 0;
    }

/* Indicator (nếu dùng) */
.indicator {
    margin-top: 12px;
    text-align: center;
}

.carousel-card-empty {
    background: transparent;
    box-shadow: none;
    border: none;
    pointer-events: none;
}
