@model webclever.Models.Category

@{
    ViewData["Title"] = "Chi tiết danh mục";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chi tiết danh mục</h1>
        <a asp-area="Admin" asp-controller="Categories" asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-2">Tên danh mục</dt>
                <dd class="col-sm-10">@Model.Name</dd>

                <dt class="col-sm-2"><PERSON><PERSON> <PERSON><PERSON></dt>
                <dd class="col-sm-10">@Model.Description</dd>

                <dt class="col-sm-4"><PERSON><PERSON> sản phẩm</dt>
                <dd class="col-sm-8">
                    <span class="badge badge-info">@Model.Products?.Count sản phẩm</span>
                </dd>
            </dl>

            <div>
                <a asp-area="Admin" asp-controller="Categories" asp-action="Edit" asp-route-id="@Model.CategoryId" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Chỉnh sửa
                </a>
            </div>
        </div>
    </div>

    @if (Model.Products != null && Model.Products.Any())
    {
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Danh sách sản phẩm</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Tên sản phẩm</th>
                                <th>Thương hiệu</th>
                                <th>Số biến thể</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var product in Model.Products)
                            {
                                <tr>
                                    <td>@product.Name</td>
                                    <td>@product.Brand</td>
                                    <td>
                                        <span class="badge badge-info">@product.ProductVariants?.Count biến thể</span>
                                    </td>
                                    <td>
                                        <a asp-controller="Products" asp-action="Details" asp-route-id="@product.ProductId" class="btn btn-info btn-sm">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div> 