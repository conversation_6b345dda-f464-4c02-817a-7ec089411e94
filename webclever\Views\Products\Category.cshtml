@model List<webclever.Models.Product>

@{
    var category = ViewBag.Category as webclever.Models.Category;
    ViewData["Title"] = category?.Name ?? "Sản phẩm";
    var currentSeries = ViewBag.CurrentSeries as string ?? "Tất cả";
    var sortBy = ViewBag.SortBy as string ?? "featured";

    // Define URLs for sorting options to avoid C# in attribute declaration error
    var featuredUrl = Url.Action("Category", "Products", new { id = category.CategoryId, series = currentSeries, sortBy = "featured" });
    var newestUrl = Url.Action("Category", "Products", new { id = category.CategoryId, series = currentSeries, sortBy = "newest" });
    var priceAscUrl = Url.Action("Category", "Products", new { id = category.CategoryId, series = currentSeries, sortBy = "price_asc" });
    var priceDescUrl = Url.Action("Category", "Products", new { id = category.CategoryId, series = currentSeries, sortBy = "price_desc" });
}

<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background-color: #2a2a2a;
        color: white;
    }

    /* Header */
    .header {
        background-color: #1a1a1a;
        padding: 10px 0;
        border-bottom: 1px solid #333;
    }

    .header-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
    }

    .logo {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .logo-text {
        font-size: 24px;
        font-weight: bold;
        color: white;
    }

    .topzone {
        color: #007aff;
    }

    .premium-text {
        font-size: 12px;
        color: #888;
    }

    .nav-menu {
        display: flex;
        gap: 30px;
        list-style: none;
    }

        .nav-menu a {
            color: white;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s;
        }

            .nav-menu a:hover {
                color: #007aff;
            }

    .header-icons {
        display: flex;
        gap: 15px;
        align-items: center;
    }

    .icon {
        width: 20px;
        height: 20px;
        background-color: #666;
        border-radius: 3px;
    }

    /* Main Container */
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    /* iPhone Title */
    .page-title {
        text-align: center;
        margin: 30px 0;
    }

    .iphone-logo {
        font-size: 48px;
        font-weight: 300;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .apple-icon {
        font-size: 40px;
    }

    /* Hero Banner */
    .hero-banner {
        background: linear-gradient(135deg, #ff6b6b, #ffd93d, #6bcf7f, #4d79ff);
        border-radius: 20px;
        padding: 40px;
        margin: 30px 300px;
        position: relative;
        overflow: hidden;
    }

    .hero-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .hero-text {
        flex: 1;
    }

    .right-month {
        font-size: 48px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .right {
        color: #ff4757;
    }

    .month {
        color: #2ed573;
    }

    .hero-subtitle {
        font-size: 24px;
        color: #333;
        margin-bottom: 20px;
    }

    .hero-phones {
        flex: 1;
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .phone-image {
        width: 80px;
        height: 140px;
        background: linear-gradient(145deg, #333, #666);
        border-radius: 20px;
        position: relative;
        border: 2px solid #999;
    }

        .phone-image::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 8px;
            background-color: #000;
            border-radius: 4px;
        }

    .hero-promo {
        flex: 1;
        text-align: right;
    }

    .promo-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .promo-price {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .promo-discount {
        background-color: #ff4757;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 10px;
    }

    .promo-details {
        font-size: 12px;
        color: #333;
    }

    /* Series Navigation */
    .series-nav {
        display: flex;
        gap: 30px;
        margin: 40px 300px 20px 300px;
        border-bottom: 1px solid #444;
        padding-bottom: 15px;
    }

    .series-link {
        color: #999;
        text-decoration: none;
        padding: 8px 0;
        font-size: 16px;
        transition: color 0.3s;
        position: relative;
    }

        .series-link.active {
            color: white;
        }

            .series-link.active::after {
                content: '';
                position: absolute;
                bottom: -15px;
                left: 0;
                right: 0;
                height: 2px;
                background-color: #007aff;
            }

        .series-link:hover {
            color: white;
        }

    /* Sort Filter */
    .filter-bar {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 30px;
    }

    .sort-select {
        background-color: #333;
        color: white;
        border: 1px solid #555;
        padding: 8px 12px;
        border-radius: 5px;
        font-size: 14px;
        margin-right: 100px;
    }

    /* Product Grid */
    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 30px;
        margin-left: 300px;
        margin-right: 300px;
    }

    .product-card {
        background-color: #333;
        border-radius: 15px;
        padding: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
        width: 350px;
    }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

    .product-image-container {
        height: 250px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        background-color: #2a2a2a;
        border-radius: 10px;
    }

    .product-main-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 10px;
    }

    .product-phone {
        width: 120px;
        height: 200px;
        background: linear-gradient(145deg, #555, #777);
        border-radius: 25px;
        position: relative;
        border: 3px solid #999;
    }

        .product-phone.gold {
            background: linear-gradient(145deg, #d4af37, #f4e4bc);
            border-color: #b8860b;
        }

        .product-phone.silver {
            background: linear-gradient(145deg, #c0c0c0, #e8e8e8);
            border-color: #a0a0a0;
        }

        .product-phone.blue {
            background: linear-gradient(145deg, #4169e1, #87ceeb);
            border-color: #191970;
        }

        .product-phone::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 10px;
            background-color: #000;
            border-radius: 5px;
        }

        .product-phone::after {
            content: '';
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 4px;
            background-color: #000;
            border-radius: 2px;
        }

    .storage-options {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-bottom: 15px;
    }

    .storage-option {
        background-color: #444;
        color: white;
        padding: 6px 12px;
        border-radius: 5px;
        font-size: 12px;
        font-weight: bold;
    }

    .product-name {
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .product-price-group {
        text-align: center;
        margin-bottom: 10px;
    }

    .product-current-price {
        font-size: 16px;
        color: #ffd93d;
        font-weight: bold;
        display: block;
    }

    .product-old-price {
        font-size: 14px;
        color: #999;
        text-decoration: line-through;
        margin-right: 10px;
    }

    .product-discount-percent {
        font-size: 12px;
        color: #ff4757;
        background-color: rgba(255, 71, 87, 0.2);
        padding: 2px 6px;
        border-radius: 3px;
    }

    .product-promo-text {
        text-align: center;
        font-size: 12px;
        color: #ffd93d;
        font-style: italic;
    }
</style>


    <nav class="series-nav">
        <a href="@Url.Action("Category", "Products", new { id = category?.CategoryId, series = "Tất cả", sortBy = sortBy })" class="series-link @("Tất cả" == currentSeries ? "active" : "")">Tất cả</a>
        @foreach (var series in ViewBag.ProductSeries)
        {
            @if (series != "Tất cả") // Avoid duplicating "Tất cả" link
            {
                <a href="@Url.Action("Category", "Products", new { id = category?.CategoryId, series = series, sortBy = sortBy })" class="series-link @(series == currentSeries ? "active" : "")">
                    @series
                </a>
            }
        }
    </nav>

    <div class="filter-bar">
        <select class="sort-select" onchange="location = this.value;">
            @{
                var options = new List<Tuple<string, string, string>>
                {
                    Tuple.Create("featured", "Xếp theo: Nổi bật", featuredUrl),
                    Tuple.Create("newest", "Mới nhất", newestUrl),
                    Tuple.Create("price_asc", "Giá tăng dần", priceAscUrl),
                    Tuple.Create("price_desc", "Giá giảm dần", priceDescUrl)
                };

                foreach (var opt in options)
                {
                    var selectedAttribute = (sortBy == opt.Item1) ? "selected" : "";
                    @Html.Raw($"<option value=\"{opt.Item3}\" {selectedAttribute}>{opt.Item2}</option>")
                }
            }
        </select>
    </div>

    @if (Model != null && Model.Any())
    {
        <div class="product-grid">
            @foreach (var product in Model)
            {
                <div class="product-card">
                    <a href="/Products/Details/@product.ProductId" style="text-decoration: none; color: inherit;">
                        <div class="product-image-container">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" alt="@product.Name" class="product-main-image">
                            }
                            else if (product.ProductVariants != null && product.ProductVariants.Any() && !string.IsNullOrEmpty(product.ProductVariants.FirstOrDefault()?.ImageUrl))
                            {
                                <img src="@product.ProductVariants.FirstOrDefault()?.ImageUrl" alt="@product.Name" class="product-main-image">
                            }
                            else
                            {
                                <img src="https://via.placeholder.com/200x200?text=No+Image" alt="No Image Available" class="product-main-image">
                            }
                        </div>
                        <div class="storage-options">
                            @if (product.ProductVariants != null)
                            {
                                @foreach (var variant in product.ProductVariants.OrderBy(v => v.Price).GroupBy(v => v.Storage).Select(g => g.First()))
                                {
                                    <span class="storage-option">@variant.Storage</span>
                                }
                            }
                        </div>
                        <div class="product-name">@product.Name</div>
                        @if (product.ProductVariants != null && product.ProductVariants.Any())
                        {
                            var minPrice = product.ProductVariants.Min(pv => pv.Price);
                            // Assuming 4,000,000₫ is a generic discount for illustration. Adjust as needed.
                            var oldPrice = minPrice + 4000000; 
                            var discountPercentage = (oldPrice.HasValue && minPrice.HasValue && oldPrice.Value > 0)
                                ? Math.Round(((oldPrice.Value - minPrice.Value) / oldPrice.Value) * 100)
                                : 0;

                            <div class="product-price-group">
                                <span class="product-current-price">@(minPrice.HasValue ? string.Format("{0:N0}₫", minPrice) : "")</span>
                                @if (oldPrice.HasValue && oldPrice.Value > minPrice.GetValueOrDefault())
                                {
                                    <span class="product-old-price">@(string.Format("{0:N0}₫", oldPrice))</span>
                                    <span class="product-discount-percent">-@discountPercentage%</span>
                                }
                            </div>
                        }
                        <div class="product-promo-text">Online giá rẻ quá</div>
                    </a>
                </div>
            }
        </div>
    }
    else
    {
        <div class="no-products-message">
            Hiện tại không có sản phẩm nào thuộc danh mục này @(currentSeries != "Tất cả" ? " với dòng sản phẩm " + currentSeries : "").
        </div>
    }
</div>