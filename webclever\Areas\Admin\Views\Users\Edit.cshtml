@model webclever.Models.User

@{
    ViewData["Title"] = "Chỉnh sửa thông tin khách hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chỉnh sửa thông tin khách hàng</h1>
        <a asp-area="Admin" asp-controller="Users" asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form asp-action="Edit">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="UserId" />
                
                <div class="form-group mb-3">
                    <label asp-for="FullName" class="control-label"><PERSON><PERSON> tên</label>
                    <input asp-for="FullName" class="form-control" />
                    <span asp-validation-for="FullName" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Email" class="control-label">Email</label>
                    <input asp-for="Email" class="form-control" />
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Phone" class="control-label">Số điện thoại</label>
                    <input asp-for="Phone" class="form-control" />
                    <span asp-validation-for="Phone" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Address" class="control-label">Địa chỉ</label>
                    <textarea asp-for="Address" class="form-control" rows="3"></textarea>
                    <span asp-validation-for="Address" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="RankId" class="control-label">Hạng khách hàng</label>
                    <select asp-for="RankId" class="form-control" asp-items="@(new SelectList(ViewBag.Ranks, "RankId", "RankName"))">
                        <option value="">-- Chọn hạng --</option>
                    </select>
                    <span asp-validation-for="RankId" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 