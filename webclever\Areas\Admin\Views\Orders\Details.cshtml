@model webclever.Models.Order

@{
    ViewData["Title"] = "Chi tiết đơn hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chi tiết đơn hàng</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin đơn hàng</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Mã đơn hàng:</dt>
                                <dd class="col-sm-8">@Model.OrderId</dd>

                                <dt class="col-sm-4">Ngày đặt:</dt>
                                <dd class="col-sm-8">@Model.OrderDate?.ToString("dd/MM/yyyy HH:mm")</dd>

                                <dt class="col-sm-4">Tổng tiền:</dt>
                                <dd class="col-sm-8">@Model.TotalAmount?.ToString("N0") đ</dd>

                                <dt class="col-sm-4">Trạng thái:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge @(Model.OrderStatus switch {
                                        "Pending" => "bg-warning",
                                        "Processing" => "bg-info",
                                        "Shipped" => "bg-primary",
                                        "Delivered" => "bg-success",
                                        "Cancelled" => "bg-danger",
                                        _ => "bg-secondary"
                                    })">
                                        @Model.OrderStatus
                                    </span>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Khách hàng:</dt>
                                <dd class="col-sm-8">@Model.User?.FullName</dd>

                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8">@Model.User?.Email</dd>

                                <dt class="col-sm-4">Số điện thoại:</dt>
                                <dd class="col-sm-8">@Model.User?.Phone</dd>

                                <dt class="col-sm-4">Mã giảm giá:</dt>
                                <dd class="col-sm-8">@(Model.Voucher?.Code ?? "Không có")</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Chi tiết sản phẩm</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Sản phẩm</th>
                                    <th>Phân loại</th>
                                    <th>Đơn giá</th>
                                    <th>Số lượng</th>
                                    <th>Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.OrderDetails)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(item.Variant?.Product?.ImageUrl))
                                                {
                                                    <img src="@item.Variant.Product.ImageUrl" alt="@item.Variant.Product.Name" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                                }
                                                <div class="ms-2">
                                                    <div>@item.Variant?.Product?.Name</div>
                                                    <small class="text-muted">@item.Variant?.Product?.Brand</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>@item.Variant?.Color - @item.Variant?.Storage</td>
                                        <td>@item.Price?.ToString("N0") đ</td>
                                        <td>@item.Quantity</td>
                                        <td>@((item.Price * item.Quantity)?.ToString("N0")) đ</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thao tác</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-action="Edit" asp-route-id="@Model.OrderId" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Cập nhật trạng thái
                        </a>
                        <a asp-action="Delete" asp-route-id="@Model.OrderId" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xóa đơn hàng
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 