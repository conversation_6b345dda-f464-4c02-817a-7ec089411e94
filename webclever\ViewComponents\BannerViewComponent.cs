using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webclever.Models;

namespace webclever.ViewComponents
{
    public class BannerViewComponent : ViewComponent
    {
        private readonly WebcleverContext _context;

        public BannerViewComponent(WebcleverContext context)
        {
            _context = context;
        }

        public async Task<IViewComponentResult> InvokeAsync(string position = "main")
        {
            var banners = await _context.Banners
                .Where(b => b.IsActive == true)
                .OrderBy(b => b.DisplayOrder)
                .ToListAsync();

            ViewBag.Position = position;
            return View(banners);
        }
    }
}
