﻿function startCountdown() {
    // <PERSON><PERSON>y thời gian hiện tại
    const now = new Date();

    // Tạo thời gian kết thúc hôm nay (23:59:59)
    const end = new Date();
    end.setHours(23, 59, 59, 999);

    const countdownElement = document.getElementById("countdown");

    function updateCountdown() {
        const now = new Date();
        const diff = end - now;

        if (diff <= 0) {
            countdownElement.innerHTML = "00 : 00 : 00";
            clearInterval(interval);
            return;
        }

        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);

        countdownElement.innerHTML =
            String(hours).padStart(2, '0') + " : " +
            String(minutes).padStart(2, '0') + " : " +
            String(seconds).padStart(2, '0');
    }

    updateCountdown(); // chạy lần đầu
    const interval = setInterval(updateCountdown, 1000);
}

// Bắt đầu khi trang tải
document.addEventListener("DOMContentLoaded", startCountdown);
