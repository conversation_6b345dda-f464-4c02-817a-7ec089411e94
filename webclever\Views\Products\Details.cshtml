@model webclever.Models.Product

@{
    ViewData["Title"] = Model.Name ?? "Chi tiết sản phẩm";
    ViewData["HideBanner"] = true; // Ẩn banner cho trang này
}

@Html.AntiForgeryToken()

<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background-color: #000;
        color: white;
    }

    /* Main Content */
    .product-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .product-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        margin: 30px 0;
    }

    /* Left Column - Images */
    .product-images {
        display: flex;
        gap: 20px;
    }

    .thumbnail-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        border: 2px solid transparent;
        cursor: pointer;
        object-fit: cover;
        transition: border-color 0.3s;
    }

    .thumbnail:hover,
    .thumbnail.active {
        border-color: #007aff;
    }

    .main-image-container {
        flex: 1;
        background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
        border-radius: 20px;
        padding: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 500px;
    }

    .main-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    /* Right Column - Product Info */
    .product-info {
        padding: 20px 0;
    }

    .product-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
        line-height: 1.3;
    }

    .product-subtitle {
        color: #999;
        font-size: 14px;
        margin-bottom: 20px;
    }

    .price-section {
        margin-bottom: 30px;
    }

    .current-price {
        font-size: 32px;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 5px;
    }

    .original-price {
        font-size: 18px;
        color: #999;
        text-decoration: line-through;
        margin-right: 10px;
    }

    .discount-badge {
        background: linear-gradient(45deg, #ff6b35, #f7931e);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
    }

    .promotion-badge {
        background: linear-gradient(45deg, #ffd700, #ffed4e);
        color: #333;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        margin: 10px 0;
        display: inline-block;
    }

    /* Storage Options */
    .storage-section {
        margin-bottom: 25px;
    }

    .section-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
    }

    .storage-options {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .storage-option {
        padding: 8px 16px;
        border: 1px solid #444;
        border-radius: 8px;
        background-color: #2a2a2a;
        color: white;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
    }

    .storage-option:hover,
    .storage-option.active {
        border-color: #007aff;
        background-color: #007aff;
    }

    /* Color Options */
    .color-section {
        margin-bottom: 25px;
    }

    .color-options {
        display: flex;
        gap: 10px;
    }

    .color-option {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid transparent;
        cursor: pointer;
        transition: border-color 0.3s;
        position: relative;
    }

    .color-option:hover,
    .color-option.active {
        border-color: white;
    }

    .color-option.gold {
        background: linear-gradient(45deg, #d4af37, #ffd700);
    }

    .color-option.silver {
        background: linear-gradient(45deg, #c0c0c0, #e5e5e5);
    }

    .color-option.black {
        background: linear-gradient(45deg, #2a2a2a, #4a4a4a);
    }

    /* Promotion Info */
    .promotion-info {
        background-color: #1a1a1a;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
    }

    .promotion-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #ff6b35;
    }

    .promotion-list {
        list-style: none;
    }

    .promotion-list li {
        font-size: 14px;
        color: #ccc;
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
    }

    .promotion-list li:before {
        content: "•";
        color: #ff6b35;
        position: absolute;
        left: 0;
    }

    /* Buy Buttons */
    .button-group {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }

    .buy-button, .add-to-cart-button {
        border: none;
        padding: 15px 30px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        flex: 1;
        transition: transform 0.2s;
    }

    .buy-button {
        background: linear-gradient(45deg, #007aff, #0056b3);
        color: white;
    }

    .add-to-cart-button {
        background: white;
        color: #007aff;
        border: 2px solid #007aff;
    }

    .buy-button:hover, .add-to-cart-button:hover {
        transform: translateY(-2px);
    }

    .add-to-cart-button:hover {
        background: #f0f8ff;
    }

    .back-button {
        display: inline-block;
        background-color: #333;
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        text-decoration: none;
        font-size: 14px;
        margin-bottom: 20px;
        transition: background-color 0.3s;
    }

    .back-button:hover {
        background-color: #444;
        color: white;
        text-decoration: none;
    }
</style>

<div class="product-container">
    <a href="javascript:history.back()" class="back-button">← Quay lại</a>

    <div class="product-layout">
        <!-- Left Column - Images -->
        <div class="product-images">
            <div class="thumbnail-list">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="thumbnail active">
                }
                @if (Model.ProductVariants != null && Model.ProductVariants.Any())
                {
                    @foreach (var variant in Model.ProductVariants.Take(4))
                    {
                        @if (!string.IsNullOrEmpty(variant.ImageUrl))
                        {
                            <img src="@variant.ImageUrl" alt="@variant.Color @variant.Storage" class="thumbnail">
                        }
                    }
                }
            </div>

            <div class="main-image-container">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="main-image">
                }
                else if (Model.ProductVariants != null && Model.ProductVariants.Any() && !string.IsNullOrEmpty(Model.ProductVariants.FirstOrDefault()?.ImageUrl))
                {
                    <img src="@Model.ProductVariants.FirstOrDefault()?.ImageUrl" alt="@Model.Name" class="main-image">
                }
                else
                {
                    <img src="https://via.placeholder.com/400x400?text=No+Image" alt="No Image Available" class="main-image">
                }
            </div>
        </div>

        <!-- Right Column - Product Info -->
        <div class="product-info">
            <h1 class="product-title">@Model.Name</h1>
            <div class="product-subtitle">Giá và khuyến mãi tại Hà Nội</div>

            <div class="price-section">
                @{
                    var firstVariant = Model.ProductVariants?.OrderBy(v => v.Price).FirstOrDefault();
                    var originalPrice = firstVariant?.Price * 1.15m; // Giả sử giá gốc cao hơn 15%
                }

                @if (firstVariant?.Price.HasValue == true)
                {
                    <div class="current-price" id="current-price">@string.Format("{0:N0}₫", firstVariant.Price)</div>
                    <div>
                        <span class="original-price" id="original-price">@string.Format("{0:N0}₫", originalPrice)</span>
                        <span class="discount-badge" id="discount-badge">-11%</span>
                    </div>
                }
                else
                {
                    <div class="current-price" id="current-price">Liên hệ</div>
                }

                <div class="promotion-badge">🎁 +40.580 điểm tích lũy Quà Tặng VIP</div>
            </div>

            <!-- Storage Options -->
            <div class="storage-section">
                <div class="section-title">Dung lượng</div>
                <div class="storage-options">
                    @if (Model.ProductVariants != null && Model.ProductVariants.Any())
                    {
                        @foreach (var storage in Model.ProductVariants.Where(v => !string.IsNullOrEmpty(v.Storage)).Select(v => v.Storage).Distinct())
                        {
                            var variantForStorage = Model.ProductVariants.Where(v => v.Storage == storage).OrderBy(v => v.Price).FirstOrDefault();
                            <div class="storage-option @(Model.ProductVariants.First().Storage == storage ? "active" : "")"
                                 data-storage="@storage"
                                 data-variant-id="@variantForStorage?.VariantId"
                                 data-price="@variantForStorage?.Price"
                                 data-color="@variantForStorage?.Color">
                                @storage
                            </div>
                        }
                    }
                    else
                    {
                        <div class="storage-option active" data-storage="256GB" data-price="25000000">256GB</div>
                        <div class="storage-option" data-storage="512GB" data-price="30000000">512GB</div>
                        <div class="storage-option" data-storage="1TB" data-price="35000000">1TB</div>
                    }
                </div>
            </div>

            <!-- Color Options -->
            <div class="color-section">
                <div class="section-title" id="color-title">Màu: @(Model.ProductVariants?.FirstOrDefault()?.Color ?? "Titan Tự Nhiên")</div>
                <div class="color-options">
                    @if (Model.ProductVariants != null && Model.ProductVariants.Any())
                    {
                        @foreach (var color in Model.ProductVariants.Where(v => !string.IsNullOrEmpty(v.Color)).Select(v => v.Color).Distinct())
                        {
                            var variantForColor = Model.ProductVariants.Where(v => v.Color == color).OrderBy(v => v.Price).FirstOrDefault();
                            var colorClass = color.ToLower().Contains("gold") || color.ToLower().Contains("vàng") ? "gold" :
                                           color.ToLower().Contains("silver") || color.ToLower().Contains("bạc") ? "silver" : "black";
                            <div class="color-option @colorClass @(Model.ProductVariants.First().Color == color ? "active" : "")"
                                 data-color="@color"
                                 data-variant-id="@variantForColor?.VariantId"
                                 data-price="@variantForColor?.Price"
                                 data-storage="@variantForColor?.Storage"
                                 title="@color"></div>
                        }
                    }
                    else
                    {
                        <div class="color-option gold active" data-color="Titan Tự Nhiên" title="Titan Tự Nhiên"></div>
                        <div class="color-option silver" data-color="Titan Trắng" title="Titan Trắng"></div>
                        <div class="color-option black" data-color="Titan Đen" title="Titan Đen"></div>
                    }
                </div>
            </div>

            <!-- Promotion Info -->
            <div class="promotion-info">
                <div class="promotion-title">Khuyến mãi trị giá 600.000₫</div>
                <ul class="promotion-list">
                    <li>Phiếu mua hàng AirPods, Apple Watch, Macbook và các sản phẩm khác lên đến 23.917.000₫</li>
                    <li>Phiếu mua hàng sử dụng máy cũ để mua máy mới, Hỗ trợ chính hãng Kantan Swap trị giá 1.000.000₫</li>
                    <li>Thu cũ đổi mới: Giảm đến 2.000.000 đồng khi và đổi thành công (Xem chi tiết tại đây)</li>
                    <li>Nhận ngay MIỄN PHÍ gói bảo hiểm VIP trị giá 150.000₫ (áp dụng cho gói 1 năm hàng chính hãng từ VNPAY (Xem chi tiết tại đây)</li>
                </ul>
            </div>

            <!-- Buy Buttons -->
            <div class="button-group">
                <button class="add-to-cart-button" onclick="addToCart()">
                    <i class="bi bi-cart-plus"></i> Thêm vào giỏ hàng
                </button>
                <button class="buy-button" onclick="buyNow()">Mua ngay</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Thumbnail image switching
    document.addEventListener('DOMContentLoaded', function() {
        const thumbnails = document.querySelectorAll('.thumbnail');
        const mainImage = document.querySelector('.main-image');

        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Remove active class from all thumbnails
                thumbnails.forEach(t => t.classList.remove('active'));

                // Add active class to clicked thumbnail
                this.classList.add('active');

                // Update main image
                if (mainImage) {
                    mainImage.src = this.src;
                    mainImage.alt = this.alt;
                }
            });
        });

        // Storage option selection
        const storageOptions = document.querySelectorAll('.storage-option');
        storageOptions.forEach(option => {
            option.addEventListener('click', function() {
                storageOptions.forEach(o => o.classList.remove('active'));
                this.classList.add('active');

                // Update price when storage changes
                updatePrice();
                updateSelectedVariant();
            });
        });

        // Color option selection
        const colorOptions = document.querySelectorAll('.color-option');
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                colorOptions.forEach(o => o.classList.remove('active'));
                this.classList.add('active');

                // Update color title
                const colorTitle = document.getElementById('color-title');
                const selectedColor = this.getAttribute('data-color');
                if (colorTitle && selectedColor) {
                    colorTitle.textContent = 'Màu: ' + selectedColor;
                }

                // Update price when color changes
                updatePrice();
                updateSelectedVariant();
            });
        });

        // Initialize selected variant
        updateSelectedVariant();
    });

    // Global variable to store current selected variant
    let currentSelectedVariantId = @(Model.ProductVariants?.FirstOrDefault()?.VariantId ?? 0);

    // Function to update price based on selected storage and color
    function updatePrice() {
        const selectedStorage = document.querySelector('.storage-option.active')?.getAttribute('data-storage');
        const selectedColor = document.querySelector('.color-option.active')?.getAttribute('data-color');

        // Find matching variant
        const variants = @Html.Raw(Json.Serialize(Model.ProductVariants?.Select(v => new {
            VariantId = v.VariantId,
            Storage = v.Storage,
            Color = v.Color,
            Price = v.Price,
            ImageUrl = v.ImageUrl
        }) ?? new object[0]));

        const matchingVariant = variants.find(v =>
            v.Storage === selectedStorage && v.Color === selectedColor
        ) || variants.find(v =>
            v.Storage === selectedStorage
        ) || variants.find(v =>
            v.Color === selectedColor
        ) || variants[0];

        if (matchingVariant && matchingVariant.Price) {
            const currentPrice = matchingVariant.Price;
            const originalPrice = currentPrice * 1.15; // 15% markup for original price
            const discountPercent = Math.round(((originalPrice - currentPrice) / originalPrice) * 100);

            // Update price display
            document.getElementById('current-price').textContent = new Intl.NumberFormat('vi-VN').format(currentPrice) + '₫';
            document.getElementById('original-price').textContent = new Intl.NumberFormat('vi-VN').format(originalPrice) + '₫';
            document.getElementById('discount-badge').textContent = '-' + discountPercent + '%';

            // Update main image if variant has image
            if (matchingVariant.ImageUrl) {
                const mainImage = document.querySelector('.main-image');
                if (mainImage) {
                    mainImage.src = matchingVariant.ImageUrl;
                }
            }
        }
    }

    // Function to update selected variant ID
    function updateSelectedVariant() {
        const selectedStorage = document.querySelector('.storage-option.active')?.getAttribute('data-storage');
        const selectedColor = document.querySelector('.color-option.active')?.getAttribute('data-color');

        const variants = @Html.Raw(Json.Serialize(Model.ProductVariants?.Select(v => new {
            VariantId = v.VariantId,
            Storage = v.Storage,
            Color = v.Color,
            Price = v.Price
        }) ?? new object[0]));

        const matchingVariant = variants.find(v =>
            v.Storage === selectedStorage && v.Color === selectedColor
        ) || variants.find(v =>
            v.Storage === selectedStorage
        ) || variants.find(v =>
            v.Color === selectedColor
        ) || variants[0];

        if (matchingVariant) {
            currentSelectedVariantId = matchingVariant.VariantId;
        }
    }

    // Add to cart function
    function addToCart() {
        const variantId = currentSelectedVariantId;

        if (variantId === 0) {
            alert('Không tìm thấy thông tin sản phẩm');
            return;
        }

        const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

        const formData = new FormData();
        formData.append('variantId', variantId);
        formData.append('quantity', 1);
        formData.append('__RequestVerificationToken', token);

        fetch('/Cart/AddToCart', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Đã thêm sản phẩm vào giỏ hàng!');
                updateCartCount();
            } else {
                alert(data.message || 'Có lỗi xảy ra khi thêm vào giỏ hàng');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi thêm vào giỏ hàng');
        });
    }

    // Buy now function
    function buyNow() {
        const variantId = currentSelectedVariantId;

        if (variantId === 0) {
            alert('Không tìm thấy thông tin sản phẩm');
            return;
        }

        const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

        const formData = new FormData();
        formData.append('variantId', variantId);
        formData.append('quantity', 1);
        formData.append('__RequestVerificationToken', token);

        fetch('/Cart/AddToCart', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Chuyển hướng đến trang giỏ hàng
                window.location.href = '/Cart';
            } else {
                alert(data.message || 'Có lỗi xảy ra khi thêm vào giỏ hàng');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi thêm vào giỏ hàng');
        });
    }

    // Update cart count in header
    function updateCartCount() {
        fetch('/Cart/GetCartCount')
        .then(response => response.json())
        .then(data => {
            // Cập nhật số lượng giỏ hàng ở header nếu có
            const cartCountElement = document.querySelector('.cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.count;
            }
        });
    }
</script>
