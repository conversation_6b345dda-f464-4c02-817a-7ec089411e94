@model webclever.Models.Product

@{
    ViewData["Title"] = Model.Name ?? "Chi tiết sản phẩm";
    ViewData["HideBanner"] = true; // Ẩn banner cho trang này
}

<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
        background-color: #000;
        color: white;
    }

    /* Main Content */
    .product-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .product-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        margin: 30px 0;
    }

    /* Left Column - Images */
    .product-images {
        display: flex;
        gap: 20px;
    }

    .thumbnail-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        border: 2px solid transparent;
        cursor: pointer;
        object-fit: cover;
        transition: border-color 0.3s;
    }

    .thumbnail:hover,
    .thumbnail.active {
        border-color: #007aff;
    }

    .main-image-container {
        flex: 1;
        background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
        border-radius: 20px;
        padding: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 500px;
    }

    .main-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    /* Right Column - Product Info */
    .product-info {
        padding: 20px 0;
    }

    .product-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
        line-height: 1.3;
    }

    .product-subtitle {
        color: #999;
        font-size: 14px;
        margin-bottom: 20px;
    }

    .price-section {
        margin-bottom: 30px;
    }

    .current-price {
        font-size: 32px;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 5px;
    }

    .original-price {
        font-size: 18px;
        color: #999;
        text-decoration: line-through;
        margin-right: 10px;
    }

    .discount-badge {
        background: linear-gradient(45deg, #ff6b35, #f7931e);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
    }

    .promotion-badge {
        background: linear-gradient(45deg, #ffd700, #ffed4e);
        color: #333;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        margin: 10px 0;
        display: inline-block;
    }

    /* Storage Options */
    .storage-section {
        margin-bottom: 25px;
    }

    .section-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
    }

    .storage-options {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .storage-option {
        padding: 8px 16px;
        border: 1px solid #444;
        border-radius: 8px;
        background-color: #2a2a2a;
        color: white;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
    }

    .storage-option:hover,
    .storage-option.active {
        border-color: #007aff;
        background-color: #007aff;
    }

    /* Color Options */
    .color-section {
        margin-bottom: 25px;
    }

    .color-options {
        display: flex;
        gap: 10px;
    }

    .color-option {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid transparent;
        cursor: pointer;
        transition: border-color 0.3s;
        position: relative;
    }

    .color-option:hover,
    .color-option.active {
        border-color: white;
    }

    .color-option.gold {
        background: linear-gradient(45deg, #d4af37, #ffd700);
    }

    .color-option.silver {
        background: linear-gradient(45deg, #c0c0c0, #e5e5e5);
    }

    .color-option.black {
        background: linear-gradient(45deg, #2a2a2a, #4a4a4a);
    }

    /* Promotion Info */
    .promotion-info {
        background-color: #1a1a1a;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
    }

    .promotion-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #ff6b35;
    }

    .promotion-list {
        list-style: none;
    }

    .promotion-list li {
        font-size: 14px;
        color: #ccc;
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
    }

    .promotion-list li:before {
        content: "•";
        color: #ff6b35;
        position: absolute;
        left: 0;
    }

    /* Buy Button */
    .buy-button {
        background: linear-gradient(45deg, #007aff, #0056b3);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        width: 100%;
        transition: transform 0.2s;
    }

    .buy-button:hover {
        transform: translateY(-2px);
    }

    .back-button {
        display: inline-block;
        background-color: #333;
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        text-decoration: none;
        font-size: 14px;
        margin-bottom: 20px;
        transition: background-color 0.3s;
    }

    .back-button:hover {
        background-color: #444;
        color: white;
        text-decoration: none;
    }
</style>

<div class="product-container">
    <a href="javascript:history.back()" class="back-button">← Quay lại</a>

    <div class="product-layout">
        <!-- Left Column - Images -->
        <div class="product-images">
            <div class="thumbnail-list">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="thumbnail active">
                }
                @if (Model.ProductVariants != null && Model.ProductVariants.Any())
                {
                    @foreach (var variant in Model.ProductVariants.Take(4))
                    {
                        @if (!string.IsNullOrEmpty(variant.ImageUrl))
                        {
                            <img src="@variant.ImageUrl" alt="@variant.Color @variant.Storage" class="thumbnail">
                        }
                    }
                }
            </div>

            <div class="main-image-container">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="main-image">
                }
                else if (Model.ProductVariants != null && Model.ProductVariants.Any() && !string.IsNullOrEmpty(Model.ProductVariants.FirstOrDefault()?.ImageUrl))
                {
                    <img src="@Model.ProductVariants.FirstOrDefault()?.ImageUrl" alt="@Model.Name" class="main-image">
                }
                else
                {
                    <img src="https://via.placeholder.com/400x400?text=No+Image" alt="No Image Available" class="main-image">
                }
            </div>
        </div>

        <!-- Right Column - Product Info -->
        <div class="product-info">
            <h1 class="product-title">@Model.Name</h1>
            <div class="product-subtitle">Giá và khuyến mãi tại Hà Nội</div>

            <div class="price-section">
                @{
                    var firstVariant = Model.ProductVariants?.OrderBy(v => v.Price).FirstOrDefault();
                    var originalPrice = firstVariant?.Price * 1.15m; // Giả sử giá gốc cao hơn 15%
                }

                @if (firstVariant?.Price.HasValue == true)
                {
                    <div class="current-price">@string.Format("{0:N0}₫", firstVariant.Price)</div>
                    <div>
                        <span class="original-price">@string.Format("{0:N0}₫", originalPrice)</span>
                        <span class="discount-badge">-11%</span>
                    </div>
                }
                else
                {
                    <div class="current-price">Liên hệ</div>
                }

                <div class="promotion-badge">🎁 +40.580 điểm tích lũy Quà Tặng VIP</div>
            </div>

            <!-- Storage Options -->
            <div class="storage-section">
                <div class="section-title">Dung lượng</div>
                <div class="storage-options">
                    @if (Model.ProductVariants != null && Model.ProductVariants.Any())
                    {
                        @foreach (var storage in Model.ProductVariants.Where(v => !string.IsNullOrEmpty(v.Storage)).Select(v => v.Storage).Distinct())
                        {
                            <div class="storage-option @(Model.ProductVariants.First().Storage == storage ? "active" : "")">@storage</div>
                        }
                    }
                    else
                    {
                        <div class="storage-option active">256GB</div>
                        <div class="storage-option">512GB</div>
                        <div class="storage-option">1TB</div>
                    }
                </div>
            </div>

            <!-- Color Options -->
            <div class="color-section">
                <div class="section-title">Màu: Titan Tự Nhiên</div>
                <div class="color-options">
                    <div class="color-option gold active"></div>
                    <div class="color-option silver"></div>
                    <div class="color-option black"></div>
                </div>
            </div>

            <!-- Promotion Info -->
            <div class="promotion-info">
                <div class="promotion-title">Khuyến mãi trị giá 600.000₫</div>
                <ul class="promotion-list">
                    <li>Phiếu mua hàng AirPods, Apple Watch, Macbook và các sản phẩm khác lên đến 23.917.000₫</li>
                    <li>Phiếu mua hàng sử dụng máy cũ để mua máy mới, Hỗ trợ chính hãng Kantan Swap trị giá 1.000.000₫</li>
                    <li>Thu cũ đổi mới: Giảm đến 2.000.000 đồng khi và đổi thành công (Xem chi tiết tại đây)</li>
                    <li>Nhận ngay MIỄN PHÍ gói bảo hiểm VIP trị giá 150.000₫ (áp dụng cho gói 1 năm hàng chính hãng từ VNPAY (Xem chi tiết tại đây)</li>
                </ul>
            </div>

            <!-- Buy Button -->
            <button class="buy-button">Mua ngay</button>
        </div>
    </div>
</div>

<script>
    // Thumbnail image switching
    document.addEventListener('DOMContentLoaded', function() {
        const thumbnails = document.querySelectorAll('.thumbnail');
        const mainImage = document.querySelector('.main-image');

        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Remove active class from all thumbnails
                thumbnails.forEach(t => t.classList.remove('active'));

                // Add active class to clicked thumbnail
                this.classList.add('active');

                // Update main image
                if (mainImage) {
                    mainImage.src = this.src;
                    mainImage.alt = this.alt;
                }
            });
        });

        // Storage option selection
        const storageOptions = document.querySelectorAll('.storage-option');
        storageOptions.forEach(option => {
            option.addEventListener('click', function() {
                storageOptions.forEach(o => o.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Color option selection
        const colorOptions = document.querySelectorAll('.color-option');
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                colorOptions.forEach(o => o.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Buy button click
        const buyButton = document.querySelector('.buy-button');
        if (buyButton) {
            buyButton.addEventListener('click', function() {
                alert('Chức năng mua hàng sẽ được phát triển trong tương lai!');
            });
        }
    });
</script>
