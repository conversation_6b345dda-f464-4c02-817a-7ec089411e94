@model webclever.Models.Product

@{
    ViewData["Title"] = Model.Name ?? "<PERSON> tiết sản phẩm";
}

<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background-color: #2a2a2a;
        color: white;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .product-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        margin: 30px 0;
    }

    .product-image-section {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #333;
        border-radius: 15px;
        padding: 40px;
        min-height: 500px;
    }

    .product-main-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 10px;
    }

    .product-info-section {
        padding: 20px;
    }

    .category-badge {
        display: inline-block;
        background-color: #007aff;
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        margin-bottom: 15px;
    }

    .product-title {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 20px;
        line-height: 1.2;
    }

    .product-description {
        font-size: 16px;
        color: #ccc;
        margin-bottom: 30px;
        line-height: 1.6;
    }

    .variants-section {
        margin-bottom: 30px;
    }

    .variants-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
    }

    .variants-grid {
        display: grid;
        gap: 15px;
    }

    .variant-item {
        background-color: #444;
        border-radius: 10px;
        padding: 15px;
        border: 2px solid transparent;
        transition: border-color 0.3s;
    }

    .variant-item:hover {
        border-color: #007aff;
    }

    .variant-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .variant-color-storage {
        font-weight: bold;
    }

    .variant-price {
        font-size: 18px;
        color: #ffd93d;
        font-weight: bold;
    }

    .variant-stock {
        font-size: 14px;
        color: #999;
    }

    .stock-available {
        color: #2ed573;
    }

    .stock-low {
        color: #ffa502;
    }

    .stock-out {
        color: #ff4757;
    }

    .back-button {
        display: inline-block;
        background-color: #007aff;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: bold;
        margin-bottom: 20px;
        transition: background-color 0.3s;
    }

    .back-button:hover {
        background-color: #0056b3;
        color: white;
        text-decoration: none;
    }

    .no-variants {
        text-align: center;
        color: #999;
        font-style: italic;
        padding: 20px;
    }


</style>

<div class="container">
    <a href="javascript:history.back()" class="back-button">← Quay lại</a>
    
    <div class="product-details">
        <div class="product-image-section">
            @if (!string.IsNullOrEmpty(Model.ImageUrl))
            {
                <img src="@Model.ImageUrl" alt="@Model.Name" class="product-main-image">
            }
            else if (Model.ProductVariants != null && Model.ProductVariants.Any() && !string.IsNullOrEmpty(Model.ProductVariants.FirstOrDefault()?.ImageUrl))
            {
                <img src="@Model.ProductVariants.FirstOrDefault()?.ImageUrl" alt="@Model.Name" class="product-main-image">
            }
            else
            {
                <img src="https://via.placeholder.com/400x400?text=No+Image" alt="No Image Available" class="product-main-image">
            }
        </div>

        <div class="product-info-section">
            @if (Model.Category != null)
            {
                <div class="category-badge">@Model.Category.Name</div>
            }
            
            <h1 class="product-title">@Model.Name</h1>
            
            @if (!string.IsNullOrEmpty(Model.Description))
            {
                <div class="product-description">@Model.Description</div>
            }

            <div class="variants-section">
                <h3 class="variants-title">Các phiên bản có sẵn</h3>
                
                @if (Model.ProductVariants != null && Model.ProductVariants.Any())
                {
                    <div class="variants-grid">
                        @foreach (var variant in Model.ProductVariants.OrderBy(v => v.Price))
                        {
                            <div class="variant-item">
                                <div class="variant-header">
                                    <div class="variant-color-storage">
                                        @if (!string.IsNullOrEmpty(variant.Color))
                                        {
                                            <span>@variant.Color</span>
                                        }
                                        @if (!string.IsNullOrEmpty(variant.Storage))
                                        {
                                            @if (!string.IsNullOrEmpty(variant.Color))
                                            {
                                                <span> - </span>
                                            }
                                            <span>@variant.Storage</span>
                                        }
                                    </div>
                                    <div class="variant-price">
                                        @(variant.Price.HasValue ? string.Format("{0:N0}₫", variant.Price) : "Liên hệ")
                                    </div>
                                </div>
                                <div class="variant-stock">
                                    @if (variant.Stock.HasValue)
                                    {
                                        @if (variant.Stock.Value > 10)
                                        {
                                            <span class="stock-available">Còn hàng (@variant.Stock.Value sản phẩm)</span>
                                        }
                                        else if (variant.Stock.Value > 0)
                                        {
                                            <span class="stock-low">Sắp hết hàng (@variant.Stock.Value sản phẩm)</span>
                                        }
                                        else
                                        {
                                            <span class="stock-out">Hết hàng</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="stock-available">Còn hàng</span>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="no-variants">Chưa có thông tin phiên bản cho sản phẩm này.</div>
                }
            </div>
        </div>
    </div>
</div>
