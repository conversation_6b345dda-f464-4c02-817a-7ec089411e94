using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims; // Để sử dụng ClaimTypes
using webclever.Models; // Namespace của User model và WebcleverContext
using Microsoft.EntityFrameworkCore; // Để sử dụng .Include()

namespace webclever.Controllers
{
    [Authorize] // Yêu cầu xác thực để truy cập các hành động trong controller này
    public class CustomerController : Controller
    {
        private readonly WebcleverContext _context;

        public CustomerController(WebcleverContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Profile()
        {
            var userEmail = User.FindFirstValue(ClaimTypes.Email); // Lấy email của người dùng từ Claims

            if (string.IsNullOrEmpty(userEmail))
            {
                return NotFound(); // Hoặc chuyển hướng đến trang lỗi/đăng nhập
            }

            var user = await _context.Users
                               .Include(u => u.Rank) // <PERSON>o gồm thông tin Rank nếu có
                               .FirstOrDefaultAsync(u => u.Email == userEmail);

            if (user == null)
            {
                return NotFound(); // Không tìm thấy người dùng trong DB
            }

            return View(user);
        }
    }
} 