@model IEnumerable<webclever.Models.Promotion>

@{
    ViewData["Title"] = "Quản lý khuyến mãi";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Quản lý khuyến mãi</h1>
        <a a asp-area="Admin" asp-controller="Promotions" asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm khuyến mãi
        </a>
    </div>

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">
            @TempData["Error"]
        </div>
    }

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="promotionsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> mãi</th>
                            <th><PERSON><PERSON> tả</th>
                            <th>Phần trăm giảm</th>
                            <th>Ngày bắt đầu</th>
                            <th>Ngày kết thúc</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.Title</td>
                                <td>@item.Description</td>
                                <td>@(item.DiscountPercent.HasValue ? string.Format("{0:N0}%", item.DiscountPercent.Value) : "N/A")</td>
                                <td>@(item.StartDate.HasValue ? string.Format("{0:dd/MM/yyyy}", item.StartDate.Value) : "N/A")</td>
                                <td>@(item.EndDate.HasValue ? string.Format("{0:dd/MM/yyyy}", item.EndDate.Value) : "N/A")</td>
                                @{
                                    var today = DateOnly.FromDateTime(DateTime.Now);
                                }
                                <td>
                                    @if (item.StartDate.HasValue && item.EndDate.HasValue)
                                    {
                                        if (item.StartDate.Value > today)
                                        {
                                            <span class="badge bg-secondary">Chưa bắt đầu</span>
                                        }
                                        else if (item.EndDate.Value < today)
                                        {
                                            <span class="badge bg-danger">Đã kết thúc</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-success">Đang diễn ra</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning">Không xác định</span>
                                    }
                                </td>
                                <td>
                                    <a asp-action="Edit" asp-route-id="@item.PromotionId" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i> Sửa
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.PromotionId" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> Xóa
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#promotionsTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Vietnamese.json"
                },
                "order": [[3, "desc"]] // Sắp xếp theo ngày bắt đầu giảm dần
            });
        });
    </script>
} 