@model IEnumerable<webclever.Models.Review>

@{
    ViewData["Title"] = "Quản lý đánh giá";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <h1 class="mt-4">Quản lý đ<PERSON>h giá</h1>

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">
            @TempData["Error"]
        </div>
    }

    <div class="card mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="reviewsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Sản phẩm</th>
                            <th>Kh<PERSON>ch hàng</th>
                            <th>Đánh giá</th>
                            <th>Nội dung</th>
                            <th>Ngày đánh giá</th>
                            <th>T<PERSON> tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if (!string.IsNullOrEmpty(item.Variant?.Product?.ImageUrl))
                                        {
                                            <img src="@item.Variant.Product.ImageUrl" alt="@item.Variant.Product.Name" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        }
                                        <div class="ms-2">
                                            <div>@item.Variant?.Product?.Name</div>
                                            <small class="text-muted">@item.Variant?.Color - @item.Variant?.Storage</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @item.User?.FullName
                                    <br />
                                    <small class="text-muted">@item.User?.Email</small>
                                </td>
                                <td>
                                    <div class="text-warning">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="fas fa-star @(i <= item.Rating ? "text-warning" : "text-muted")"></i>
                                        }
                                    </div>
                                    <small class="text-muted">@item.Rating/5</small>
                                </td>
                                <td>@item.Comment</td>
                                <td>@item.CreatedAt?.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>
                                    <a asp-action="Details" asp-route-id="@item.ReviewId" class="btn btn-info btn-sm">
                                        <i class="fas fa-info-circle"></i> Chi tiết
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.ReviewId" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> Xóa
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#reviewsTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Vietnamese.json"
                },
                "order": [[4, "desc"]] // Sắp xếp theo ngày đánh giá giảm dần
            });
        });
    </script>
} 