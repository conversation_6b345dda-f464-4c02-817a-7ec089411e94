@model IEnumerable<webclever.Models.Banner>

@{
    ViewData["Title"] = "Danh sách banner";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Danh sách banner</h1>
        <a asp-area="Admin" asp-controller="Banners" asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm mới
        </a>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tiêu đề</th>
                            <th>Hình ảnh</th>
                            <th>Link</th>
                            <th>Vị trí</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.BannerId</td>
                                <td>@item.Title</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                                    {
                                        <img src="@item.ImageUrl" alt="@item.Title" style="max-width: 200px; max-height: 100px;" />
                                    }
                                </td>
                                <td>@item.Link</td>
                                @* <td>@item.Position</td>*@
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-area="Admin" asp-controller="Banners" asp-action="Edit" asp-route-id="@item.BannerId" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Banners" asp-action="Details" asp-route-id="@item.BannerId" class="btn btn-info btn-sm">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Banners" asp-action="Delete" asp-route-id="@item.BannerId" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#dataTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[0, 'desc']], // Sắp xếp theo ID giảm dần
                columnDefs: [
                    { orderable: false, targets: [2, 5] } // Không cho phép sắp xếp cột hình ảnh và thao tác
                ]
            });
        });
    </script>
} 