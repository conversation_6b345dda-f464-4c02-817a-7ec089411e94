@model List<webclever.Models.Cart>

@{
    ViewData["Title"] = "Giỏ hàng của bạn";
    ViewData["HideBanner"] = true; // Ẩn banner cho trang giỏ hàng
}

@Html.AntiForgeryToken()

<style>
    body {
        background-color: #f5f5f5;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    }

    .cart-container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 0 20px;
    }

    .breadcrumb-nav {
        background: none;
        padding: 0;
        margin-bottom: 20px;
    }

    .breadcrumb-nav a {
        color: #666;
        text-decoration: none;
        font-size: 14px;
    }

    .breadcrumb-nav a:hover {
        color: #007aff;
    }

    .cart-content {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 20px;
    }

    .cart-items {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .cart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .cart-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    .store-info {
        font-size: 14px;
        color: #666;
    }

    .cart-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .cart-item:last-child {
        border-bottom: none;
    }

    .item-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        margin-right: 15px;
    }

    .item-details {
        flex: 1;
    }

    .item-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
    }

    .item-variant {
        font-size: 14px;
        color: #666;
        margin-bottom: 5px;
    }

    .item-price {
        font-size: 16px;
        font-weight: 600;
        color: #d70018;
    }

    .item-original-price {
        font-size: 14px;
        color: #999;
        text-decoration: line-through;
        margin-left: 10px;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        margin: 10px 0;
    }

    .quantity-btn {
        width: 30px;
        height: 30px;
        border: 1px solid #ddd;
        background: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .quantity-input {
        width: 50px;
        height: 30px;
        text-align: center;
        border: 1px solid #ddd;
        border-left: none;
        border-right: none;
    }

    .cart-summary {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        height: fit-content;
    }

    .summary-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .summary-total {
        display: flex;
        justify-content: space-between;
        font-size: 18px;
        font-weight: 600;
        color: #d70018;
        padding-top: 15px;
        border-top: 1px solid #eee;
        margin-top: 15px;
    }

    .checkout-btn {
        width: 100%;
        background: #007aff;
        color: white;
        border: none;
        padding: 15px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        margin-top: 20px;
    }

    .checkout-btn:hover {
        background: #0056b3;
    }

    .empty-cart {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .empty-cart-icon {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 20px;
    }

    .continue-shopping {
        background: #007aff;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        display: inline-block;
        margin-top: 20px;
    }

    @@media (max-width: 768px) {
        .cart-content {
            grid-template-columns: 1fr;
        }

        .cart-item {
            flex-direction: column;
            align-items: flex-start;
        }

        .item-image {
            margin-bottom: 10px;
        }
    }
</style>

<div class="cart-container">
    <!-- Breadcrumb -->
    <nav class="breadcrumb-nav">
        <a href="/">← Về trang chủ TopZone</a>
    </nav>

    @if (Model.Any())
    {
        <div class="cart-content">
            <!-- Cart Items -->
            <div class="cart-items">
                <div class="cart-header">
                    <div class="cart-title">Giỏ hàng của bạn</div>
                    <div class="store-info">Tạm tính (@Model.Sum(c => c.Quantity) sản phẩm):</div>
                </div>

                @foreach (var item in Model)
                {
                    <div class="cart-item" data-cart-id="@item.CartId">
                        <img src="@(item.Variant?.Product?.ImageUrl ?? "/images/no-image.png")" 
                             alt="@item.Variant?.Product?.Name" 
                             class="item-image" />
                        
                        <div class="item-details">
                            <div class="item-name">@item.Variant?.Product?.Name</div>
                            <div class="item-variant">
                                @item.Variant?.Storage
                            </div>
                            <div class="item-variant">4 Khuyến mãi</div>
                            <div class="item-variant">@item.Variant?.Color</div>

                            <div class="quantity-controls">
                                <button class="quantity-btn" onclick="updateQuantity(@item.CartId, @(item.Quantity - 1))">-</button>
                                <input type="number" class="quantity-input" value="@item.Quantity"
                                       onchange="updateQuantity(@item.CartId, this.value)" min="1" />
                                <button class="quantity-btn" onclick="updateQuantity(@item.CartId, @(item.Quantity + 1))">+</button>
                            </div>
                        </div>
                        
                        <div>
                            <div class="item-price">
                                @((item.Variant?.Price ?? 0).ToString("N0"))₫
                            </div>
                            <button class="btn btn-sm btn-outline-danger mt-2" onclick="removeItem(@item.CartId)">
                                <i class="bi bi-trash"></i> Xóa
                            </button>
                        </div>
                    </div>
                }
            </div>

            <!-- Cart Summary -->
            <div class="cart-summary">
                <div class="summary-title">Thông tin khách hàng</div>
                
                <div class="form-group mb-3">
                    <label>Anh/Chị</label>
                    <div class="d-flex gap-2">
                        <label><input type="radio" name="gender" value="male" checked> Anh</label>
                        <label><input type="radio" name="gender" value="female"> Chị</label>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <input type="text" class="form-control" placeholder="Họ và Tên" required />
                </div>
                
                <div class="form-group mb-3">
                    <input type="tel" class="form-control" placeholder="Số điện thoại" required />
                </div>
                
                <div class="summary-title mt-4">Chọn hình thức nhận hàng</div>
                
                <div class="form-group mb-3">
                    <label><input type="radio" name="delivery" value="store" checked> Giao tận nơi</label>
                    <label><input type="radio" name="delivery" value="pickup"> Nhận tại cửa hàng</label>
                </div>
                
                <select class="form-control mb-2">
                    <option>Hồ Chí Minh</option>
                </select>
                
                <select class="form-control mb-2">
                    <option>Chọn Quận / Huyện</option>
                </select>
                
                <select class="form-control mb-3">
                    <option>Chọn Phường / Xã</option>
                </select>
                
                <input type="text" class="form-control mb-3" placeholder="Số nhà, tên đường" />
                
                <div class="form-group mb-3">
                    <label>
                        <input type="checkbox"> Gọi người khác nhận hàng
                    </label>
                    <label>
                        <input type="checkbox"> Chuyển sang hóa đơn đỏ (dành cho doanh nghiệp)
                    </label>
                    <label>
                        <input type="checkbox"> Xuất hóa đơn công ty
                    </label>
                </div>
                
                <div class="summary-row">
                    <span>Tổng tiền:</span>
                    <span class="text-danger fw-bold">@Model.Sum(c => (c.Variant?.Price ?? 0) * (c.Quantity ?? 0)).ToString("N0")₫</span>
                </div>
                
                <div class="summary-row">
                    <span>Điểm tích lũy Quà Tặng VIP:</span>
                    <span>@(Model.Sum(c => (c.Variant?.Price ?? 0) * (c.Quantity ?? 0)) / 1000) điểm</span>
                </div>
                
                <div class="form-group mt-3">
                    <label>
                        <input type="checkbox"> Tôi đồng ý với <a href="#">Chính sách xử lý dữ liệu cá nhân của TopZone</a>
                    </label>
                </div>
                
                <button class="checkout-btn">Đặt hàng</button>
                
                <p class="text-center mt-2 small">
                    Bạn có thể chọn hình thức thanh toán sau khi đặt hàng
                </p>
            </div>
        </div>
    }
    else
    {
        <div class="cart-items">
            <div class="empty-cart">
                <div class="empty-cart-icon">🛒</div>
                <h3>Giỏ hàng của bạn đang trống</h3>
                <p>Hãy thêm sản phẩm vào giỏ hàng để tiếp tục mua sắm</p>
                <a href="/" class="continue-shopping">Tiếp tục mua sắm</a>
            </div>
        </div>
    }
</div>

<script>
function updateQuantity(cartId, newQuantity) {
    if (newQuantity < 1) {
        if (confirm('Bạn có muốn xóa sản phẩm này khỏi giỏ hàng?')) {
            removeItem(cartId);
        }
        return;
    }

    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;
    const formData = new FormData();
    formData.append('cartId', cartId);
    formData.append('quantity', newQuantity);
    formData.append('__RequestVerificationToken', token);

    fetch('/Cart/UpdateQuantity', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'Có lỗi xảy ra');
        }
    });
}

function removeItem(cartId) {
    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;
    const formData = new FormData();
    formData.append('cartId', cartId);
    formData.append('__RequestVerificationToken', token);

    fetch('/Cart/RemoveItem', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'Có lỗi xảy ra');
        }
    });
}
</script>
