@model List<webclever.Models.Cart>

@{
    ViewData["Title"] = "Giỏ hàng của bạn";
    ViewData["HideBanner"] = true; // Ẩn banner cho trang giỏ hàng
}

@Html.AntiForgeryToken()

<style>
    body {
        background-color: #f5f5f5;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    }

    .cart-container {
        max-width: 900px;
        margin: 32px auto 0 auto;
        padding: 0 12px;
    }

    .breadcrumb-nav {
        margin-bottom: 18px;
    }

    .breadcrumb-nav a {
        color: #666;
        text-decoration: none;
        font-size: 14px;
    }

    .breadcrumb-nav a:hover {
        color: #007aff;
    }

    .cart-content {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
    }

    .cart-items {
        flex: 2;
        background: #fff;
        border-radius: 10px;
        padding: 20px 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.07);
        min-width: 320px;
    }

    .cart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 18px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .cart-title {
        font-size: 20px;
        font-weight: 700;
        color: #222;
    }

    .store-info {
        font-size: 15px;
        color: #888;
    }

    .cart-item {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
        gap: 16px;
    }

    .cart-item:last-child {
        border-bottom: none;
    }

    .item-image {
        width: 72px;
        height: 72px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    }

    .item-details {
        flex: 1;
    }

    .item-name {
        font-size: 16px;
        font-weight: 600;
        color: #222;
        margin-bottom: 2px;
    }

    .item-variant, .item-variant-extra {
        font-size: 13px;
        color: #888;
        margin-bottom: 2px;
    }

    .item-price {
        font-size: 16px;
        font-weight: 700;
        color: #d70018;
        margin-bottom: 6px;
    }

    .item-original-price {
        font-size: 13px;
        color: #bbb;
        text-decoration: line-through;
        margin-left: 8px;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        margin: 8px 0;
        gap: 4px;
    }

    .quantity-btn {
        width: 28px;
        height: 28px;
        border: 1px solid #ddd;
        background: #fafbfc;
        cursor: pointer;
        border-radius: 6px;
        font-size: 18px;
        color: #007aff;
        transition: background 0.2s;
    }

    .quantity-btn:hover {
        background: #e6f0ff;
    }

    .quantity-input {
        width: 40px;
        height: 28px;
        text-align: center;
        border: 1px solid #ddd;
        border-left: none;
        border-right: none;
        border-radius: 0;
        font-size: 15px;
    }

    .cart-summary {
        flex: 1;
        background: #fff;
        border-radius: 10px;
        padding: 20px 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.07);
        min-width: 260px;
        height: fit-content;
    }

    .summary-title {
        font-size: 17px;
        font-weight: 700;
        margin-bottom: 14px;
        color: #222;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .summary-total {
        display: flex;
        justify-content: space-between;
        font-size: 18px;
        font-weight: 700;
        color: #d70018;
        padding-top: 12px;
        border-top: 1px solid #eee;
        margin-top: 12px;
    }

    .checkout-btn {
        width: 100%;
        background: linear-gradient(90deg,#007aff,#0056b3);
        color: white;
        border: none;
        padding: 13px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 700;
        cursor: pointer;
        margin-top: 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        transition: background 0.2s;
    }

    .checkout-btn:hover {
        background: #0056b3;
    }

    .empty-cart {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .empty-cart-icon {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 20px;
    }

    .continue-shopping {
        background: #007aff;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        display: inline-block;
        margin-top: 20px;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }

    .continue-shopping:hover {
        background: #0056b3;
    }

    @media (max-width: 900px) {
        .cart-content {
            flex-direction: column;
        }

        .cart-summary, .cart-items {
            min-width: 0;
        }
    }
</style>

<div class="cart-container">
    <!-- Breadcrumb -->
    <nav class="breadcrumb-nav">
        <a href="/">← Về trang chủ TopZone</a>
    </nav>

    @if (Model.Any())
    {
        <div class="cart-content">
            <!-- Cart Items -->
            <div class="cart-items">
                <div class="cart-header">
                    <div class="cart-title">Giỏ hàng của bạn</div>
                    <div class="store-info">Tạm tính (@Model.Sum(c => c.Quantity) sản phẩm):</div>
                </div>

                @foreach (var item in Model)
                {
                    <div class="cart-item" data-cart-id="@item.CartId">
                        <img src="@(item.Variant?.Product?.ImageUrl ?? "/images/no-image.png")" 
                             alt="@item.Variant?.Product?.Name" 
                             class="item-image" />
                        
                        <div class="item-details">
                            <div class="item-name">@item.Variant?.Product?.Name</div>
                            <div class="item-variant">@item.Variant?.Storage @item.Variant?.Color</div>
                            <div class="item-variant-extra">4 Khuyến mãi</div>

                            <div class="quantity-controls">
                                <button class="quantity-btn" onclick="updateQuantity(@item.CartId, @(item.Quantity - 1))">-</button>
                                <input type="number" class="quantity-input" value="@item.Quantity"
                                       onchange="updateQuantity(@item.CartId, this.value)" min="1" />
                                <button class="quantity-btn" onclick="updateQuantity(@item.CartId, @(item.Quantity + 1))">+</button>
                            </div>
                        </div>
                        
                        <div style="text-align:right; min-width:90px;">
                            <div class="item-price">
                                @((item.Variant?.Price ?? 0).ToString("N0"))₫
                                @if ((item.Variant?.OriginalPrice ?? 0) > (item.Variant?.Price ?? 0))
                                {
                                    <span class="item-original-price">@((item.Variant?.OriginalPrice ?? 0).ToString("N0"))₫</span>
                                }
                            </div>
                            <button class="btn btn-sm btn-outline-danger mt-2" onclick="removeItem(@item.CartId)" title="Xóa sản phẩm">
                                <i class="bi bi-trash"></i> Xóa
                            </button>
                        </div>
                    </div>
                }
            </div>

            <!-- Cart Summary -->
            <div class="cart-summary">
                <div class="summary-title">Thông tin khách hàng</div>
                
                <div class="form-group mb-2">
                    <label style="font-size:14px;">Anh/Chị</label>
                    <div class="d-flex gap-2">
                        <label><input type="radio" name="gender" value="male" checked> Anh</label>
                        <label><input type="radio" name="gender" value="female"> Chị</label>
                    </div>
                </div>
                
                <div class="form-group mb-2">
                    <input type="text" class="form-control" placeholder="Họ và Tên" required />
                </div>
                
                <div class="form-group mb-2">
                    <input type="tel" class="form-control" placeholder="Số điện thoại" required />
                </div>
                
                <div class="summary-title mt-3">Chọn hình thức nhận hàng</div>
                
                <div class="form-group mb-2">
                    <label><input type="radio" name="delivery" value="store" checked> Giao tận nơi</label>
                    <label><input type="radio" name="delivery" value="pickup"> Nhận tại cửa hàng</label>
                </div>
                
                <select class="form-control mb-2">
                    <option>Hồ Chí Minh</option>
                </select>
                
                <select class="form-control mb-2">
                    <option>Chọn Quận / Huyện</option>
                </select>
                
                <select class="form-control mb-2">
                    <option>Chọn Phường / Xã</option>
                </select>
                
                <input type="text" class="form-control mb-2" placeholder="Số nhà, tên đường" />
                
                <div class="form-group mb-2">
                    <label><input type="checkbox"> Gọi người khác nhận hàng</label>
                    <label><input type="checkbox"> Chuyển sang hóa đơn đỏ (dành cho doanh nghiệp)</label>
                    <label><input type="checkbox"> Xuất hóa đơn công ty</label>
                </div>
                
                <div class="summary-row">
                    <span>Tổng tiền:</span>
                    <span class="text-danger fw-bold">@Model.Sum(c => (c.Variant?.Price ?? 0) * (c.Quantity ?? 0)).ToString("N0")₫</span>
                </div>
                
                <div class="summary-row">
                    <span>Điểm tích lũy Quà Tặng VIP:</span>
                    <span>@(Model.Sum(c => (c.Variant?.Price ?? 0) * (c.Quantity ?? 0)) / 1000) điểm</span>
                </div>
                
                <div class="form-group mt-2">
                    <label>
                        <input type="checkbox"> Tôi đồng ý với <a href="#">Chính sách xử lý dữ liệu cá nhân của TopZone</a>
                    </label>
                </div>
                
                <button class="checkout-btn">Đặt hàng</button>
                
                <p class="text-center mt-2 small">
                    Bạn có thể chọn hình thức thanh toán sau khi đặt hàng
                </p>
            </div>
        </div>
    }
    else
    {
        <div class="cart-items">
            <div class="empty-cart">
                <div class="empty-cart-icon">🛒</div>
                <h3>Giỏ hàng của bạn đang trống</h3>
                <p>Hãy thêm sản phẩm vào giỏ hàng để tiếp tục mua sắm</p>
                <a href="/" class="continue-shopping">Tiếp tục mua sắm</a>
            </div>
        </div>
    }
</div>

<script>
function updateQuantity(cartId, newQuantity) {
    if (newQuantity < 1) {
        if (confirm('Bạn có muốn xóa sản phẩm này khỏi giỏ hàng?')) {
            removeItem(cartId);
        }
        return;
    }

    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;
    const formData = new FormData();
    formData.append('cartId', cartId);
    formData.append('quantity', newQuantity);
    formData.append('__RequestVerificationToken', token);

    fetch('/Cart/UpdateQuantity', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'Có lỗi xảy ra');
        }
    });
}

function removeItem(cartId) {
    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;
    const formData = new FormData();
    formData.append('cartId', cartId);
    formData.append('__RequestVerificationToken', token);

    fetch('/Cart/RemoveItem', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'Có lỗi xảy ra');
        }
    });
}
</script>
