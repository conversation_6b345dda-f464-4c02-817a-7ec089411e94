$(document).ready(function () {
    // Sidebar toggle
    $('#sidebarCollapse').off('click').on('click', function () {
        $('#sidebar').toggleClass('active');
        $('#content').toggleClass('active');
    });

    // DataTables initialization
    if ($.fn.DataTable) {
        $('.datatable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/vi.json'
            },
            responsive: true
        });
    }

    // Xử lý form tạo sản phẩm
    if ($('form[data-form-type="product-ajax-form"]').length > 0) {
        console.log('Form product-ajax-form đã được tìm thấy');
        
        // Kiểm tra jQuery và SweetAlert2
        if (typeof $ === 'undefined') {
            console.error('jQuery chưa được load');
            return;
        }
        
        if (typeof Swal === 'undefined') {
            console.error('SweetAlert2 chưa được load');
            return;
        }

        // Xử lý preview hình ảnh sản phẩm chính
        $('#imageFile').change(function() {
            const file = this.files[0];
            const preview = $('#imagePreview');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.attr('src', e.target.result);
                }
                reader.readAsDataURL(file);
            } else {
                preview.attr('src', '/images/no-image.png');
            }
        });
        
        // Xóa tất cả event handlers cũ
        $('#addVariant').off();
        $(document).off('click', '.remove-variant');

        // Xử lý thêm biến thể
        $('#addVariant').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const index = $('.variant-item').length;
            const variantHtml = `
                <div class="variant-item border p-3 mb-3">
                    <input type="hidden" name="Variants[${index}].VariantId" value="0" />
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Màu sắc</label>
                                <input type="text" class="form-control" name="Variants[${index}].Color" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Dung lượng</label>
                                <input type="text" class="form-control" name="Variants[${index}].Storage" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Giá</label>
                                <input type="number" class="form-control" name="Variants[${index}].Price" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Tồn kho</label>
                                <input type="number" class="form-control" name="Variants[${index}].Stock" required>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-danger btn-block remove-variant">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Hình ảnh biến thể</label>
                                <input type="file" class="form-control variant-image-input" name="VariantImages[${index}]" accept="image/*">
                                <div class="mt-2">
                                    <img src="/images/no-image.png" alt="Preview" class="img-thumbnail variant-preview" style="max-width: 100px;" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#variantsContainer').append(variantHtml);
        });

        // Xử lý xóa biến thể
        $(document).on('click', '.remove-variant', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).closest('.variant-item').remove();
            // Cập nhật lại index của các biến thể còn lại
            $('.variant-item').each(function(index) {
                $(this).find('input, select').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        if (name.startsWith('Variants') || name.startsWith('VariantImages')) {
                            $(this).attr('name', name.replace(/\[\d+\]/, `[${index}]`));
                        }
                    }
                });
            });
        });

        // Xử lý preview hình ảnh biến thể
        $(document).on('change', '.variant-image-input', function() {
            const file = this.files[0];
            const preview = $(this).closest('.form-group').find('.variant-preview');
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.attr('src', e.target.result);
                }
                reader.readAsDataURL(file);
            } else {
                preview.attr('src', '/images/no-image.png');
            }
        });

        // Xử lý submit form
        $('form[data-form-type="product-ajax-form"]').on('submit', function (e) {
            e.preventDefault();

            const $form = $(this);
            const form = this;

            if (!form.checkValidity()) {
                e.stopPropagation();
                form.classList.add('was-validated');
                return;
            }

            const formData = new FormData(form);

            // Vô hiệu hóa nút submit để chặn double submit
            $form.find('button[type="submit"]').prop('disabled', true);

            $.ajax({
                url: $form.attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Thành công!',
                            text: 'Sản phẩm đã được tạo thành công.',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            window.location.href = response.redirectUrl || '/Admin/Products';
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Đã xảy ra lỗi khi tạo sản phẩm.'
                        });

                        // Kích hoạt lại submit nếu có lỗi
                        $form.find('button[type="submit"]').prop('disabled', false);
                    }
                },
                error: function (xhr, status, error) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        text: 'Lỗi khi gửi dữ liệu: ' + error
                    });

                    // Kích hoạt lại submit nếu có lỗi
                    $form.find('button[type="submit"]').prop('disabled', false);
                }
            });
        });

        // Thêm biến thể đầu tiên khi trang được tải
        $('#addVariant').click();
    }

    // Sweet Alert confirmations
    $('.btn-delete').off('click').on('click', function (e) {
        e.preventDefault();
        const form = $(this).closest('form');

        Swal.fire({
            title: 'Xác nhận xóa?',
            text: "Bạn không thể hoàn tác sau khi xóa!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
        }).then((result) => {
            if (result.isConfirmed) {
                form[0].submit();
            }
        });
    });

    // Order status update
    $('.order-status').on('change', function () {
        const orderId = $(this).data('order-id');
        const newStatus = $(this).val();

        $.ajax({
            url: '/Admin/Orders/UpdateStatus',
            type: 'POST',
            data: {
                orderId: orderId,
                status: newStatus
            },
            success: function (response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Cập nhật thành công!',
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        text: response.message
                    });
                }
            },
            error: function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: 'Đã xảy ra lỗi khi cập nhật trạng thái đơn hàng.'
                });
            }
        });
    });

    // Sidebar link tracking
    $(document).on('click', '#sidebar a', function (e) {
        console.log('Sidebar link clicked. Element:', this);
        console.log('  href:', this.href);
        console.log('  pathname:', this.pathname);
        console.log('  hostname:', this.hostname);
        if (e.isDefaultPrevented()) {
            console.warn('Default action for this link was prevented.');
        } else {
            console.log('Default action for this link was NOT prevented.');
        }
    });
});