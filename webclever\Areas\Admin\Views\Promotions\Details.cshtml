<dt class="col-sm-4">T<PERSON><PERSON> k<PERSON><PERSON><PERSON>n mãi</dt>
<dd class="col-sm-8">@Model.Title</dd>
<dt class="col-sm-4">Phần trăm giảm</dt>
<dd class="col-sm-8">@(Model.DiscountPercent.HasValue ? string.Format("{0:N0}%", Model.DiscountPercent.Value) : "N/A")</dd>
<dt class="col-sm-4"><PERSON><PERSON><PERSON> bắt đầu</dt>
<dd class="col-sm-8">@(Model.StartDate.HasValue ? string.Format("{0:dd/MM/yyyy}", Model.StartDate.Value) : "N/A")</dd>
<dt class="col-sm-4"><PERSON><PERSON><PERSON> kết thúc</dt>
<dd class="col-sm-8">@(Model.EndDate.HasValue ? string.Format("{0:dd/MM/yyyy}", Model.EndDate.Value) : "N/A")</dd>
@{
    var today = DateOnly.FromDateTime(DateTime.Now);
}
@if (Model.StartDate.HasValue && Model.EndDate.HasValue)
{
    if (Model.StartDate.Value > today)
    {
        <span class="badge bg-secondary">Chưa bắt đầu</span>
    }
    else if (Model.EndDate.Value < today)
    {
        <span class="badge bg-danger">Đã kết thúc</span>
    }
    else
    {
        <span class="badge bg-success">Đang diễn ra</span>
    }
}
else
{
    <span class="badge bg-warning">Không xác định</span>
} 