﻿* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-x: hidden;
}

.container {
    text-align: center;
    padding: 20px;
}

.title {
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.3);
    }

    to {
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.6);
    }
}

.wheel-container {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

.wheel {
    width: 450px;
    height: 450px;
    border: 10px solid #fff;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 40px rgba(0,0,0,0.4);
    transition: transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.segment {
    position: absolute;
    width: 50%;
    height: 50%;
    transform-origin: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 11px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    padding: 10px;
    text-align: center;
    line-height: 1.2;
    gap: 5px;
}

    .segment img {
        width: 45px;
        height: 45px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid rgba(255,255,255,0.8);
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        transition: transform 0.3s ease;
    }

    .segment:hover img {
        transform: scale(1.1);
    }

    .segment span {
        font-size: 10px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 3px;
    }

    /* Segment colors */
    .segment:nth-child(1) {
        background: linear-gradient(45deg, #FF6B6B, #FF8E53);
        transform: rotate(0deg);
    }

    .segment:nth-child(2) {
        background: linear-gradient(45deg, #4ECDC4, #44A08D);
        transform: rotate(45deg);
    }

    .segment:nth-child(3) {
        background: linear-gradient(45deg, #45B7D1, #96C93D);
        transform: rotate(90deg);
    }

    .segment:nth-child(4) {
        background: linear-gradient(45deg, #FFA07A, #FA8072);
        transform: rotate(135deg);
    }

    .segment:nth-child(5) {
        background: linear-gradient(45deg, #98D8C8, #F7DC6F);
        transform: rotate(180deg);
    }

    .segment:nth-child(6) {
        background: linear-gradient(45deg, #F7DC6F, #F0B27A);
        transform: rotate(225deg);
    }

    .segment:nth-child(7) {
        background: linear-gradient(45deg, #BB8FCE, #8E44AD);
        transform: rotate(270deg);
    }

    .segment:nth-child(8) {
        background: linear-gradient(45deg, #85C1E9, #3498DB);
        transform: rotate(315deg);
    }

.pointer {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 40px solid #FFD700;
    z-index: 10;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.4));
}

.center-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 50%;
    border: 6px solid white;
    z-index: 5;
    box-shadow: 0 0 20px rgba(0,0,0,0.4);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo {
    font-size: 2rem;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

.spin-button {
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    color: white;
    border: none;
    padding: 18px 45px;
    font-size: 1.3rem;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

    .spin-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .spin-button:hover::before {
        left: 100%;
    }

    .spin-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    }

    .spin-button:active {
        transform: translateY(-1px);
    }

    .spin-button:disabled {
        background: linear-gradient(45deg, #ccc, #aaa);
        cursor: not-allowed;
        transform: none;
    }

.result {
    margin-top: 30px;
    padding: 25px;
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
    backdrop-filter: blur(10px);
}

    .result.show {
        opacity: 1;
        transform: translateY(0);
    }

.result-content h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.8rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.prize-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.prize-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 12px;
    border: 3px solid #FFD700;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    animation: prizeGlow 2s ease-in-out infinite alternate;
}

@keyframes prizeGlow {
    from {
        box-shadow: 0 4px 15px rgba(0,0,0,0.2), 0 0 10px rgba(255,215,0,0.3);
    }

    to {
        box-shadow: 0 4px 15px rgba(0,0,0,0.2), 0 0 20px rgba(255,215,0,0.6);
    }
}

.result p {
    color: #666;
    font-size: 1.2rem;
    margin: 0;
}

.prize-value {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1.1rem;
    display: inline-block;
    margin-top: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    z-index: 1000;
    pointer-events: none;
    animation: confetti-fall 3s linear infinite;
}

    .confetti.square {
        border-radius: 0;
    }

    .confetti.circle {
        border-radius: 50%;
    }

    .confetti.triangle {
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 10px solid;
    }

@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }

    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Secret Button Styles */
.secret-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.secret-button {
    background: linear-gradient(45deg, #2c3e50, #34495e);
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1rem;
    font-weight: bold;
    border-radius: 25px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

    .secret-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .secret-button:hover::before {
        left: 100%;
    }

    .secret-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        background: linear-gradient(45deg, #34495e, #2c3e50);
    }

    .secret-button:active {
        transform: translateY(0);
    }

/* Close Button */
.close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    z-index: 100;
}

    .close-button:hover {
        background: #ff4757;
        color: white;
        transform: rotate(90deg);
    }

/* Container Animation */
.container {
    animation: fadeInScale 0.5s ease-out;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.container.hide {
    animation: fadeOutScale 0.3s ease-in forwards;
}

@keyframes fadeOutScale {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }

    to {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
}

/* Body styles */
@media (max-width: 580px) {
    .wheel {
        width: 350px;
        height: 350px;
    }

    .title {
        font-size: 2rem;
    }

    .segment {
        font-size: 9px;
        padding: 8px;
    }

        .segment img {
            width: 35px;
            height: 35px;
        }

        .segment span {
            font-size: 8px;
        }

    .center-circle {
        width: 60px;
        height: 60px;
    }

    .logo {
        font-size: 1.5rem;
    }

    .spin-button {
        padding: 15px 35px;
        font-size: 1.1rem;
    }
}

@media (max-width: 400px) {
    .wheel {
        width: 280px;
        height: 280px;
    }

    .segment img {
        width: 25px;
        height: 25px;
    }

    .prize-container {
        flex-direction: column;
        gap: 10px;
    }
}
