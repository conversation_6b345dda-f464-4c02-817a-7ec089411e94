/* Override body padding for login page */
body {
    padding-top: 0 !important;
}

/* <PERSON><PERSON> Header Override */
.login-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    background-color: #1d1d1f;
    color: white;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    min-height: 70px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    position: relative;
    z-index: 1001;
}

.login-navbar .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: opacity 0.3s ease;
    flex-shrink: 0;
}

.login-navbar .logo:hover {
    opacity: 0.8;
}

.login-navbar .logo-text {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    letter-spacing: -0.3px;
}

.login-navbar .clever {
    margin-right: 6px;
}

.login-navbar .technology {
    color: red;
    font-weight: 700;
}

.login-navbar .logo-image {
    height: 40px;
    width: 50px;
    margin-left: 10px;
    opacity: 0.95;
    transition: opacity 0.3s ease;
}

.login-navbar .logo-image:hover {
    opacity: 1;
}

.login-navbar .nav-container {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
    margin: 0 20px;
    padding: 0;
}

.login-navbar .nav-links {
    display: flex;
    align-items: center;
    gap: 30px;
    margin: 0;
}

.login-navbar .nav-links a {
    color: #f5f5f7;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.login-navbar .nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.login-navbar .nav-icons {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-shrink: 0;
}

.login-navbar .nav-icon img {
    width: 20px;
    height: 20px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.login-navbar .nav-icon:hover img {
    opacity: 1;
}

.login-navbar .login-icon img {
    width: 20px;
    height: 20px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.login-navbar .login-icon:hover img {
    opacity: 1;
}

/* Login Page Styling */
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.login-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-title {
    color: #ffffff;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.login-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 400;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
}

.form-input {
    width: 100%;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    color: #ffffff;
    font-size: 16px;
    transition: all 0.3s ease;
    outline: none;
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-input:focus {
    background: rgba(255, 255, 255, 0.12);
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

.checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.checkbox-input {
    width: 18px;
    height: 18px;
    margin-right: 12px;
    accent-color: #007AFF;
}

.checkbox-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    cursor: pointer;
}

.login-button {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
    border: none;
    border-radius: 12px;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.login-button:hover {
    background: linear-gradient(135deg, #0056CC 0%, #003D99 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.login-links {
    text-align: center;
}

.forgot-password {
    color: #007AFF;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 15px;
    display: inline-block;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #0056CC;
}

.signup-link {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.signup-link a {
    color: #007AFF;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.signup-link a:hover {
    color: #0056CC;
}

.error-message {
    background: rgba(255, 59, 48, 0.1);
    border: 1px solid rgba(255, 59, 48, 0.3);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    color: #FF3B30;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .login-title {
        font-size: 24px;
    }
}

/* Animation for form appearance */
.login-container {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Input focus animation */
.form-input {
    position: relative;
}

.form-group {
    position: relative;
}

.form-group::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #007AFF;
    transition: width 0.3s ease;
}

.form-group:focus-within::after {
    width: 100%;
}
