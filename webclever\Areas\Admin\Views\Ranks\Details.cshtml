@model webclever.Models.CustomerRank

@{
    ViewData["Title"] = "Chi tiết hạng khách hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chi tiết hạng khách hàng</h1>
        <div>
            <a asp-action="Edit" asp-route-id="@Model.RankId" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
            </a>
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin hạng khách hàng</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">ID</dt>
                        <dd class="col-sm-9">@Model.RankId</dd>

                        <dt class="col-sm-3">Tên hạng</dt>
                        <dd class="col-sm-9">@Model.RankName</dd>

                        <dt class="col-sm-3">Chi tiêu tối thiểu</dt>
                        <dd class="col-sm-9">@(Model.MinTotalSpend.HasValue ? string.Format("{0:N0} VNĐ", Model.MinTotalSpend.Value) : "N/A")</dd>

                        <dt class="col-sm-3">Tỷ lệ giảm giá</dt>
                        <dd class="col-sm-9">@(Model.DiscountRate.HasValue ? string.Format("{0:N1}%", Model.DiscountRate.Value) : "N/A")</dd>
                    </dl>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Danh sách khách hàng</h6>
                </div>
                <div class="card-body">
                    @if (Model.Users != null && Model.Users.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered" id="usersTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Họ tên</th>
                                        <th>Email</th>
                                        <th>Số điện thoại</th>
                                        <th>Ngày tham gia</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in Model.Users)
                                    {
                                        <tr>
                                            <td>@user.UserId</td>
                                            <td>@user.FullName</td>
                                            <td>@user.Email</td>
                                            <td>@user.Phone</td>
                                            <td>@(user.CreatedAt.HasValue ? string.Format("{0:dd/MM/yyyy}", user.CreatedAt.Value) : "N/A")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Chưa có khách hàng nào thuộc hạng này
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin bổ sung</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Lưu ý</h5>
                        <ul class="mb-0">
                            <li>Hạng khách hàng được sắp xếp theo chi tiêu tối thiểu</li>
                            <li>Khách hàng sẽ được tự động nâng hạng khi đạt đủ điều kiện</li>
                            <li>Không thể xóa hạng khách hàng đang được sử dụng</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#usersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json',
                },
                order: [[0, 'desc']]
            });
        });
    </script>
} 