@model webclever.Models.Category

@{
    ViewData["Title"] = "Thêm danh mục mới";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Thêm danh mục mới</h1>
        <a asp-area="Admin" asp-controller="Categories" asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form id="categoryForm" asp-area="Admin" asp-controller="Categories" asp-action="Create" method="post">
                @Html.AntiForgeryToken()

                <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                <div class="form-group mb-3">
                    <label asp-for="Name" class="control-label">Tên danh mục <span class="text-danger">*</span></label>
                    <input asp-for="Name" class="form-control" />
                    <span asp-validation-for="Name" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Description" class="control-label">Mô tả</label>
                    <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function () {
            // Xử lý submit form
            $('#categoryForm').submit(function (e) {
                e.preventDefault();
                
                // Lấy dữ liệu form
                var formData = $(this).serialize();
                
                // Gửi request AJAX
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    success: function (response) {
                        if (response.success) {
                            // Hiển thị thông báo thành công
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: 'Danh mục đã được tạo thành công.',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                // Chuyển hướng về trang danh sách
                                window.location.href = '@Url.Action("Index", "Categories", new { area = "Admin" })';
                            });
                        } else {
                            // Hiển thị thông báo lỗi
                            Swal.fire({
                                icon: 'error',
                                title: 'Lỗi!',
                                text: response.message || 'Đã xảy ra lỗi khi tạo danh mục.'
                            });
                        }
                    },
                    error: function () {
                        // Hiển thị thông báo lỗi khi gọi API thất bại
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: 'Đã xảy ra lỗi khi xử lý yêu cầu.'
                        });
                    }
                });
            });
        });
    </script>
}