@{
    ViewData["Title"] = "Đăng nhập";
    ViewData["HideBanner"] = true;
}

@section Styles {
    <link rel="stylesheet" href="/css/login.css">
}

<!-- Login Page -->
    <div class="login-page">
        <div class="login-container">
            <div class="login-header">
                <h1 class="login-title">Đ<PERSON>ng nhập</h1>
                <p class="login-subtitle">Chào mừng bạn quay trở lại</p>
            </div>

            <form method="post" action="/Account/Login">
                @if (ViewData.ModelState.ErrorCount > 0)
                {
                    <div class="error-message">
                        @Html.ValidationSummary(false, "", new { @class = "text-danger" })
                    </div>
                }

                <div class="form-group">
                    <label for="email" class="form-label">Tên đăng nhập</label>
                    <input type="email" class="form-input" id="email" name="email" placeholder="Nhập email của bạn" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">M<PERSON>t khẩu</label>
                    <input type="password" class="form-input" id="password" name="password" placeholder="Nhập mật khẩu" required>
                </div>

                <div class="checkbox-container">
                    <input type="checkbox" class="checkbox-input" id="rememberMe" name="rememberMe">
                    <label class="checkbox-label" for="rememberMe">Ghi nhớ đăng nhập</label>
                </div>

                <button type="submit" class="login-button">Đăng nhập</button>
            </form>

            <div class="login-links">
                <a href="#" class="forgot-password">Quên mật khẩu?</a>
                <div class="signup-link">
                    Chưa có tài khoản? <a href="#">Đăng ký ngay</a>
                </div>
            </div>
        </div>
    </div>