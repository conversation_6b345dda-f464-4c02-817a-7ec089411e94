@{
    ViewData["Title"] = "Đăng nhập";
    ViewData["HideBanner"] = true;
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Clever Technology</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/master.css">
    <link rel="stylesheet" href="/css/login.css">
</head>
<body>
    <!-- Header -->
    <div class="navbar">
        <a href="/" class="logo">
            <div class="logo-text">
                <span class="clever">Clever</span> <span class="technology">Technology</span>
                <img src="/premium reseller apple banner.PNG" alt="Apple Logo" class="logo-image" />
            </div>
        </a>
        <div class="nav-container">
            <div class="nav-links">
                <a href="/">iPhone</a>
                <a href="/">Mac</a>
                <a href="/">iPad</a>
                <a href="/">Watch</a>
                <a href="/">Tai nghe, Loa</a>
                <a href="/">Phụ kiện</a>
                <a href="/">Tài chính</a>
                <a href="/">TopCare</a>
            </div>
        </div>
        <div class="nav-icons">
            <div class="nav-icon">
                <img src="https://img.icons8.com/ios-filled/50/ffffff/search.png" alt="Search" />
            </div>
            <div class="nav-icon">
                <a href="/Cart" style="color: white; text-decoration: none; position: relative;">
                    <img src="https://img.icons8.com/ios-filled/50/ffffff/shopping-cart.png" alt="Cart" />
                </a>
            </div>
            <a href="/Account/Login" class="login-icon">
                <img src="https://img.icons8.com/ios-filled/50/ffffff/user.png" alt="Login" />
            </a>
        </div>
    </div>

    <!-- Login Page -->
    <div class="login-page">
        <div class="login-container">
            <div class="login-header">
                <h1 class="login-title">Đăng nhập</h1>
                <p class="login-subtitle">Chào mừng bạn quay trở lại</p>
            </div>

            <form method="post" action="/Account/Login">
                @if (ViewData.ModelState.ErrorCount > 0)
                {
                    <div class="error-message">
                        @Html.ValidationSummary(false, "", new { @class = "text-danger" })
                    </div>
                }

                <div class="form-group">
                    <label for="email" class="form-label">Tên đăng nhập</label>
                    <input type="email" class="form-input" id="email" name="email" placeholder="Nhập email của bạn" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <input type="password" class="form-input" id="password" name="password" placeholder="Nhập mật khẩu" required>
                </div>

                <div class="checkbox-container">
                    <input type="checkbox" class="checkbox-input" id="rememberMe" name="rememberMe">
                    <label class="checkbox-label" for="rememberMe">Ghi nhớ đăng nhập</label>
                </div>

                <button type="submit" class="login-button">Đăng nhập</button>
            </form>

            <div class="login-links">
                <a href="#" class="forgot-password">Quên mật khẩu?</a>
                <div class="signup-link">
                    Chưa có tài khoản? <a href="#">Đăng ký ngay</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>