@{
    ViewData["Title"] = "Đăng nhập";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Đ<PERSON>ng nhập</h3>
                </div>
                <div class="card-body">
                    <form method="post" action="/Account/Login">
                        <div class="mb-3">
                            <label for="username" class="form-label">Tên đăng nhập</label>
                            <input type="text" class="form-control" id="username" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Mật khẩu</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe">
                            <label class="form-check-label" for="rememberMe"><PERSON><PERSON> nhớ đăng nhập</label>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Đăng nhập</button>
                        </div>
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    </form>
                    <div class="mt-3 text-center">
                        <a href="#" class="text-decoration-none">Quên mật khẩu?</a>
                    </div>
                    <div class="mt-2 text-center">
                        <span>Chưa có tài khoản? </span>
                        <a href="#" class="text-decoration-none">Đăng ký ngay</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 