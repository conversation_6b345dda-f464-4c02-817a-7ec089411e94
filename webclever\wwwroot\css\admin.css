/* Admin Layout Styles */
:root {
    --sidebar-width: 250px;
    --header-height: 60px;
    --primary-color: #343a40;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
    position: relative;
}

/* Sidebar Styles */
#sidebar {
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
    min-height: 100vh;
    transition: all 0.3s;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    overflow: visible;
}

#sidebar.active {
    margin-left: calc(-1 * var(--sidebar-width));
}

#sidebar .sidebar-header {
    padding: 20px;
    background: var(--primary-color);
}

#sidebar ul.components {
    padding: 20px 0;
    overflow: visible;
}

#sidebar ul li {
    position: relative;
    z-index: 2;
}

#sidebar ul li a {
    padding: 15px 20px;
    display: block;
    width: 100%;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s;
    cursor: pointer;
    position: relative;
    z-index: 99999;
    pointer-events: auto !important;
}

#sidebar ul li a:hover {
    background: rgba(255, 255, 255, 0.1);
}

#sidebar ul li.active > a {
    background: var(--info-color);
}

#sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Content Styles */
#content {
    width: calc(100% - var(--sidebar-width));
    min-height: 100vh;
    margin-left: var(--sidebar-width);
    transition: all 0.3s;
    position: relative;
    z-index: 1;
}

#content.active {
    width: 100%;
    margin-left: 0;
}

/* Navbar Styles */
.navbar {
    padding: 15px 10px;
    background: #fff;
    border: none;
    border-radius: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
}

#sidebarCollapse {
    background: var(--primary-color);
    border: none;
    cursor: pointer;
    z-index: 9999;
}

#sidebarCollapse:hover {
    background: var(--secondary-color);
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    background-color: #f8f9fa;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

/* Button Styles */
.btn {
    border-radius: 5px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Form Styles */
.form-control {
    border-radius: 5px;
    border: 1px solid #ddd;
    padding: 10px 15px;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
}

/* Responsive */
@media (max-width: 768px) {
    #sidebar {
        margin-left: calc(-1 * var(--sidebar-width));
    }
    #sidebar.active {
        margin-left: 0;
    }
    #content {
        width: 100%;
        margin-left: 0;
    }
    #content.active {
        width: calc(100% - var(--sidebar-width));
        margin-left: var(--sidebar-width);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Status Badges */
.badge {
    padding: 6px 10px;
    border-radius: 4px;
    font-weight: 500;
    color:black;
}

.badge-pending {
    background-color: var(--warning-color);
    color: #000;
}

.badge-paid {
    background-color: var(--success-color);
    color: #fff;
}

.badge-shipped {
    background-color: var(--info-color);
    color: #fff;
}

.badge-delivered {
    background-color: var(--success-color);
    color: #fff;
}

.badge-cancelled {
    background-color: var(--danger-color);
    color: #fff;
} 