@model webclever.Models.Banner

@{
    ViewData["Title"] = "Thêm banner";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Thêm banner</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin banner</h6>
                </div>
                <div class="card-body">
                    <form asp-action="Create" enctype="multipart/form-data">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="form-group mb-3">
                            <label asp-for="Title" class="control-label">Tiêu đ<PERSON></label>
                            <input asp-for="Title" class="form-control" />
                            <span asp-validation-for="Title" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="ImageUrl" class="control-label">Hình ảnh</label>
                            <input type="file" name="ImageFile" class="form-control" accept="image/*" />
                            <span asp-validation-for="ImageUrl" class="text-danger"></span>
                            <small class="form-text text-muted">Định dạng: JPG, PNG, GIF. Kích thước tối đa: 2MB</small>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Link" class="control-label">Link</label>
                            <input asp-for="Link" class="form-control" placeholder="https://" />
                            <span asp-validation-for="Link" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="DisplayOrder" class="control-label">Thứ tự hiển thị</label>
                            <input asp-for="DisplayOrder" class="form-control" type="number" min="0" />
                            <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">Hiển thị banner</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lưu ý</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Thông tin quan trọng</h5>
                        <ul class="mb-0">
                            <li>Kích thước hình ảnh nên đồng nhất</li>
                            <li>Định dạng hình ảnh: JPG, PNG, GIF</li>
                            <li>Dung lượng tối đa: 2MB</li>
                            <li>Thứ tự hiển thị càng nhỏ càng hiển thị trước</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 