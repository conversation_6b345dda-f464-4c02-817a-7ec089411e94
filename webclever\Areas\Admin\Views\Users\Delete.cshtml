@model webclever.Models.User

@{
    ViewData["Title"] = "Xóa khách hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Xóa khách hàng</h1>
        <a asp-area="Admin" asp-controller="Users" asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="alert alert-danger">
                <h4 class="alert-heading">Cảnh báo!</h4>
                <p>Bạn có chắc chắn muốn xóa khách hàng này không?</p>
            </div>

            <dl class="row">
                <dt class="col-sm-2"><PERSON><PERSON> tên</dt>
                <dd class="col-sm-10">@Model.FullName</dd>

                <dt class="col-sm-2">Email</dt>
                <dd class="col-sm-10">@Model.Email</dd>

                <dt class="col-sm-2">Số điện thoại</dt>
                <dd class="col-sm-10">@Model.Phone</dd>

                <dt class="col-sm-2">Địa chỉ</dt>
                <dd class="col-sm-10">@Model.Address</dd>

                <dt class="col-sm-2">Hạng khách hàng</dt>
                <dd class="col-sm-10">@(Model.Rank?.RankName ?? "Chưa có hạng")</dd>
            </dl>

            <form asp-action="Delete">
                <input type="hidden" asp-for="UserId" />
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Xác nhận xóa
                </button>
            </form>
        </div>
    </div>
</div> 