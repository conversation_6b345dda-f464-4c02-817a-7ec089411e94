@model IEnumerable<webclever.Models.Product>

@{
    ViewData["Title"] = "Quản lý sản phẩm";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Quản lý sản phẩm</h1>
        <a asp-area="Admin" asp-controller="Products" asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm sản phẩm mới
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>H<PERSON>nh ảnh</th>
                            <th>T<PERSON><PERSON> sản phẩm</th>
                            <th>Th<PERSON><PERSON><PERSON> hiệu</th>
                            <th><PERSON><PERSON> mục</th>
                            <th><PERSON><PERSON><PERSON><PERSON> thể</th>
                            <th><PERSON><PERSON> tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                                    {
                                        <img src="@item.ImageUrl" alt="@item.Name" style="width: 50px; height: 50px; object-fit: cover;" />
                                    }
                                    else
                                    {
                                        <img src="/images/no-image.png" alt="No image" style="width: 50px; height: 50px; object-fit: cover;" />
                                    }
                                </td>
                                <td>@item.Name</td>
                                <td>@item.Brand</td>
                                <td>@item.Category?.Name</td>
                                <td>
                                    @if (item.ProductVariants != null && item.ProductVariants.Any())
                                    {
                                        <span class="badge badge-info">@item.ProductVariants.Count biến thể</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-warning">Chưa có biến thể</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-area="Admin" asp-controller="Products" asp-action="Edit" asp-route-id="@item.ProductId" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Products" asp-action="Details" asp-route-id="@item.ProductId" class="btn btn-info btn-sm">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Products" asp-action="Delete" asp-route-id="@item.ProductId" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('.datatable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/vi.json'
                }
            });
        });
    </script>
} 