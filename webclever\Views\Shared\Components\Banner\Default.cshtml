@model IEnumerable<webclever.Models.Banner>

<div class="banner-container">
    <!-- Navigation Arrows -->
    <div class="arrow-buttons">
        <button class="arrow-btn prev"></button>
        <button class="arrow-btn next"></button>
    </div>
    
    <!-- Slides container -->
    <div class="slides-container">
        @if (Model != null && Model.Any())
        {
            var banners = Model.ToList();
            @for (int i = 0; i < banners.Count; i++)
            {
                var banner = banners[i];
                <div class="slide @(i == 0 ? "active" : "")">
                    @if (!string.IsNullOrEmpty(banner.Link))
                    {
                        <a href="@banner.Link" class="slide-link">
                            <div class="slide-content">
                                <div class="slide-image">
                                    <img src="@banner.ImageUrl" alt="@banner.Title" title="@banner.Title">
                                </div>
                            </div>
                        </a>
                    }
                    else
                    {
                        <div class="slide-content">
                            <div class="slide-image">
                                <img src="@banner.ImageUrl" alt="@banner.Title" title="@banner.Title">
                            </div>
                        </div>
                    }
                </div>
            }
        }
        else
        {
            <!-- Fallback slide nếu không có banner từ database -->
            <div class="slide active">
                <div class="slide-content">
                    <div class="slide-text">
                        <h2>Vượt Trội Trải Nghiệm</h2>
                        <h3>XÀI TRƯỚC TRẢ SAU - THU CŨ GIÁ CAO</h3>
                        <div class="promo-button">TRẢ GÓP 0%</div>
                        <p>TRẢI NGHIỆM "PRO" CHUẨN APPLE</p>
                        <p>Áp dụng toàn hệ thống Clever Technology</p>
                    </div>
                    <div class="slide-image">
                        <img src="~/iphone_17.jpg" alt="iPhone Banner">
                    </div>
                </div>
            </div>
        }
    </div>
    
    <!-- Dots indicator -->
    @if (Model != null && Model.Any())
    {
        var banners = Model.ToList();
        @if (banners.Count > 1)
        {
            <div class="dots-container">
                @for (int i = 0; i < banners.Count; i++)
                {
                    <span class="dot @(i == 0 ? "active" : "")" data-slide="@i"></span>
                }
            </div>
        }
    }
</div>
