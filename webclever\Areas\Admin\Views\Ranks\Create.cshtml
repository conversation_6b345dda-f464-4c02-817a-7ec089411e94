@model webclever.Models.CustomerRank

@{
    ViewData["Title"] = "Thêm hạng khách hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Thêm hạng khách hàng</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin hạng khách hàng</h6>
                </div>
                <div class="card-body">
                    <form asp-action="Create">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="form-group mb-3">
                            <label asp-for="RankName" class="control-label">Tên hạng</label>
                            <input asp-for="RankName" class="form-control" />
                            <span asp-validation-for="RankName" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="MinTotalSpend" class="control-label">Chi tiêu tối thiểu</label>
                            <div class="input-group">
                                <input asp-for="MinTotalSpend" class="form-control" type="number" min="0" step="1000" />
                                <span class="input-group-text">VNĐ</span>
                            </div>
                            <span asp-validation-for="MinTotalSpend" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="DiscountRate" class="control-label">Tỷ lệ giảm giá</label>
                            <div class="input-group">
                                <input asp-for="DiscountRate" class="form-control" type="number" min="0" max="100" step="0.1" />
                                <span class="input-group-text">%</span>
                            </div>
                            <span asp-validation-for="DiscountRate" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lưu ý</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Thông tin quan trọng</h5>
                        <ul class="mb-0">
                            <li>Tên hạng phải là duy nhất</li>
                            <li>Chi tiêu tối thiểu phải lớn hơn 0</li>
                            <li>Tỷ lệ giảm giá phải từ 0 đến 100%</li>
                            <li>Hạng khách hàng sẽ được sắp xếp theo chi tiêu tối thiểu từ thấp đến cao</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 