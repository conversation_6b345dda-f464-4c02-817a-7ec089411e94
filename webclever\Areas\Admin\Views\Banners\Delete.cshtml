@model webclever.Models.Banner

@{
    ViewData["Title"] = "Xóa banner";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Xóa banner</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Xác nhận xóa</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Cảnh báo</h5>
                        <p class="mb-0"><PERSON><PERSON><PERSON> c<PERSON> chắc chắn muốn xóa banner này? Hành động này không thể hoàn tác.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">ID</dt>
                                <dd class="col-sm-8">@Model.BannerId</dd>

                                <dt class="col-sm-4">Tiêu đề</dt>
                                <dd class="col-sm-8">@Model.Title</dd>

                                <dt class="col-sm-4">Link</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.Link))
                                    {
                                        <a href="@Model.Link" target="_blank" class="text-primary">
                                            <i class="fas fa-external-link-alt"></i> Xem link
                                        </a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Không có link</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Thứ tự</dt>
                                <dd class="col-sm-8">@Model.DisplayOrder</dd>

                                <dt class="col-sm-4">Trạng thái</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive == true)
                                    {
                                        <span class="badge bg-success">Đang hiển thị</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">Đã ẩn</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                                {
                                    <img src="@Model.ImageUrl" alt="@Model.Title" class="img-fluid rounded" style="max-height: 200px;" />
                                }
                                else
                                {
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> Không có hình ảnh
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <form asp-action="Delete" class="mt-4">
                        <input type="hidden" asp-for="BannerId" />
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xác nhận xóa
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lưu ý</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-circle"></i> Thông tin quan trọng</h5>
                        <ul class="mb-0">
                            <li>Hành động này sẽ xóa vĩnh viễn banner</li>
                            <li>Hình ảnh banner sẽ bị xóa khỏi hệ thống</li>
                            <li>Không thể khôi phục sau khi xóa</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 