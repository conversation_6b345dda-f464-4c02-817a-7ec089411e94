@model webclever.Models.Promotion

@{
    ViewData["Title"] = "Thêm khuyến mãi";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Thêm khuyến mãi</h1>
        <a asp-area="Admin" asp-controller="Promotions" asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin khuyến mãi</h6>
                </div>
                <div class="card-body">
                    <form asp-action="Create">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="form-group mb-3">
                            <label asp-for="Title" class="control-label">Tên khuyến mãi</label>
                            <input asp-for="Title" class="form-control" />
                            <span asp-validation-for="Title" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Description" class="control-label">Mô tả</label>
                            <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="DiscountPercent" class="control-label">Phần trăm giảm (%)</label>
                            <input asp-for="DiscountPercent" class="form-control" />
                            <span asp-validation-for="DiscountPercent" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="StartDate" class="control-label">Ngày bắt đầu</label>
                                    <input asp-for="StartDate" class="form-control" type="date" />
                                    <span asp-validation-for="StartDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="EndDate" class="control-label">Ngày kết thúc</label>
                                    <input asp-for="EndDate" class="form-control" type="date" />
                                    <span asp-validation-for="EndDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lưu ý</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Thông tin quan trọng</h5>
                        <ul class="mb-0">
                            <li>Tên khuyến mãi phải là duy nhất</li>
                            <li>Phần trăm giảm giá phải từ 0 đến 100%</li>
                            <li>Ngày kết thúc phải sau ngày bắt đầu</li>
                            <li>Khuyến mãi sẽ tự động kích hoạt khi đến ngày bắt đầu</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 