using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webclever.Models;

namespace webclever.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class RanksController : Controller
    {
        private readonly WebcleverContext _context;

        public RanksController(WebcleverContext context)
        {
            _context = context;
        }

        // GET: Admin/Ranks
        public async Task<IActionResult> Index()
        {
            return View(await _context.CustomerRanks.OrderBy(r => r.MinTotalSpend).ToListAsync());
        }

        // GET: Admin/Ranks/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var customerRank = await _context.CustomerRanks
                .Include(r => r.Users)
                .FirstOrDefaultAsync(m => m.RankId == id);
            if (customerRank == null)
            {
                return NotFound();
            }

            return View(customerRank);
        }

        // GET: Admin/Ranks/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Admin/Ranks/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("RankId,RankName,MinTotalSpend,DiscountRate")] CustomerRank customerRank)
        {
            if (ModelState.IsValid)
            {
                _context.Add(customerRank);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Thêm hạng khách hàng thành công!";
                return RedirectToAction(nameof(Index));
            }
            return View(customerRank);
        }

        // GET: Admin/Ranks/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var customerRank = await _context.CustomerRanks.FindAsync(id);
            if (customerRank == null)
            {
                return NotFound();
            }
            return View(customerRank);
        }

        // POST: Admin/Ranks/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("RankId,RankName,MinTotalSpend,DiscountRate")] CustomerRank customerRank)
        {
            if (id != customerRank.RankId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(customerRank);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Cập nhật hạng khách hàng thành công!";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CustomerRankExists(customerRank.RankId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(customerRank);
        }

        // GET: Admin/Ranks/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var customerRank = await _context.CustomerRanks
                .Include(r => r.Users)
                .FirstOrDefaultAsync(m => m.RankId == id);
            if (customerRank == null)
            {
                return NotFound();
            }

            return View(customerRank);
        }

        // POST: Admin/Ranks/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var customerRank = await _context.CustomerRanks
                .Include(r => r.Users)
                .FirstOrDefaultAsync(r => r.RankId == id);

            if (customerRank != null)
            {
                if (customerRank.Users.Any())
                {
                    TempData["ErrorMessage"] = "Không thể xóa hạng khách hàng này vì đang có khách hàng thuộc hạng này!";
                    return RedirectToAction(nameof(Index));
                }

                _context.CustomerRanks.Remove(customerRank);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Xóa hạng khách hàng thành công!";
            }
            return RedirectToAction(nameof(Index));
        }

        private bool CustomerRankExists(int id)
        {
            return _context.CustomerRanks.Any(e => e.RankId == id);
        }
    }
} 