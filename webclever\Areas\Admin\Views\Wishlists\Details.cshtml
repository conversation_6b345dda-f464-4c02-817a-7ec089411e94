@model webclever.Models.Wishlist

@{
    ViewData["Title"] = "Chi tiết danh sách yêu thích";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chi tiết danh sách yêu thích</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin khách hàng</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">Họ tên:</dt>
                        <dd class="col-sm-8">@Model.User?.FullName</dd>

                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">@Model.User?.Email</dd>

                        <dt class="col-sm-4">Số điện thoại:</dt>
                        <dd class="col-sm-8">@Model.User?.Phone</dd>

                        <dt class="col-sm-4">Ngày thêm:</dt>
                        <dd class="col-sm-8">@Model.AddedAt?.ToString("dd/MM/yyyy HH:mm")</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin sản phẩm</h6>
                </div>
                <div class="card-body">
                    @if (Model.Variant?.Product != null)
                    {
                        <div class="text-center mb-3">
                            @if (!string.IsNullOrEmpty(Model.Variant.Product.ImageUrl))
                            {
                                <img src="@Model.Variant.Product.ImageUrl" alt="@Model.Variant.Product.Name" class="img-fluid rounded" style="max-height: 200px;">
                            }
                        </div>
                        <dl class="row">
                            <dt class="col-sm-4">Tên sản phẩm:</dt>
                            <dd class="col-sm-8">@Model.Variant.Product.Name</dd>

                            <dt class="col-sm-4">Thương hiệu:</dt>
                            <dd class="col-sm-8">@Model.Variant.Product.Brand</dd>

                            <dt class="col-sm-4">Danh mục:</dt>
                            <dd class="col-sm-8">@Model.Variant.Product.Category?.Name</dd>

                            <dt class="col-sm-4">Phân loại:</dt>
                            <dd class="col-sm-8">@Model.Variant.Color - @Model.Variant.Storage</dd>

                            <dt class="col-sm-4">Giá:</dt>
                            <dd class="col-sm-8">@Model.Variant.Price?.ToString("N0") đ</dd>
                        </dl>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Sản phẩm không tồn tại hoặc đã bị xóa
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div> 