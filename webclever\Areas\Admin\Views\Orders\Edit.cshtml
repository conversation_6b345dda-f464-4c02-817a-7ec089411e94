@model webclever.Models.Order

@{
    ViewData["Title"] = "Cập nhật đơn hàng";
    Layout = "_AdminLayout";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Cập nhật đơn hàng</h1>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>

    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin đơn hàng</h6>
                </div>
                <div class="card-body">
                    <form asp-action="Edit">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="OrderId" />
                        <input type="hidden" asp-for="UserId" />
                        <input type="hidden" asp-for="OrderDate" />
                        <input type="hidden" asp-for="TotalAmount" />
                        <input type="hidden" asp-for="VoucherId" />

                        <div class="form-group mb-3">
                            <label asp-for="OrderStatus" class="control-label">Trạng thái đơn hàng</label>
                            <select asp-for="OrderStatus" class="form-select">
                                <option value="Pending">Chờ xử lý</option>
                                <option value="Processing">Đang xử lý</option>
                                <option value="Shipped">Đang giao hàng</option>
                                <option value="Delivered">Đã giao hàng</option>
                                <option value="Cancelled">Đã hủy</option>
                            </select>
                            <span asp-validation-for="OrderStatus" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu thay đổi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin đơn hàng</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">Mã đơn hàng:</dt>
                        <dd class="col-sm-8">@Model.OrderId</dd>

                        <dt class="col-sm-4">Ngày đặt:</dt>
                        <dd class="col-sm-8">@Model.OrderDate?.ToString("dd/MM/yyyy HH:mm")</dd>

                        <dt class="col-sm-4">Tổng tiền:</dt>
                        <dd class="col-sm-8">@Model.TotalAmount?.ToString("N0") đ</dd>

                        <dt class="col-sm-4">Khách hàng:</dt>
                        <dd class="col-sm-8">@Model.User?.FullName</dd>

                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">@Model.User?.Email</dd>

                        <dt class="col-sm-4">Mã giảm giá:</dt>
                        <dd class="col-sm-8">@(Model.Voucher?.Code ?? "Không có")</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 