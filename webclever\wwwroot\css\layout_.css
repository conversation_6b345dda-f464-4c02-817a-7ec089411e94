/* --- Layout & Base --- */
body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    margin: 0;
    background-color: #0f0f0f;
    font-family: Arial, sans-serif;
}

/* --- Navbar --- */
.navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    background-color: #0f0f0f;
    color: white;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-image {
    height: 50px;
    width: 60px;
    margin-right: 10px;
}

.logo-text {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: bold;
    color: #fff;
}

.clever {
    margin-right: 10px;
}

.technology {
    color: red;
}

.nav-container {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    align-items: center;
}

.nav-links button {
    background-color: black;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    gap: 20px;
    margin: 0 10px;
}

.nav-links button:hover {
    background-color: #555;
}

.nav-icons {
    display: flex;
    gap: 15px;
    margin-left: 250px;
}

.nav-icons img {
    width: 24px;
    height: 24px;
}

.login-icon {
    right: 200px;
}

/* --- Main Content --- */
.main-content {
    flex: 1;
    padding: 20px;
    text-align: center;
}

/* --- Footer --- */
.footer {
    background-color: #1a1a1a;
    color: white;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding-top: 20px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-brand {
    font-size: 24px;
    font-weight: bold;
}

.highlight {
    color: #ff2d55;
}

.footer-apple-logo {
    height: 24px;
    width: auto;
}

.footer-reseller {
    font-size: 14px;
    color: #ccc;
}

.footer-sections {
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.footer-section {
    flex: 1;
}

.footer-section h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: red;
}

.footer-section p a {
    font-size: 14px;
    margin-bottom: 5px;
    color: white;
    text-decoration: none;
}

.social-icons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.social-icons img {
    width: 24px;
    height: 24px;
}

.footer-bottom {
    font-size: 12px;
    color: #ccc;
    border-top: 1px solid #333;
    padding-top: 10px;
    text-align: center;
}

.footer-bottom p,
.footer-bottom a {
    margin: 5px 0;
}

/* --- Search Overlay --- */
/* --- Search Overlay --- */
.search-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.search-overlay.active {
    opacity: 1;
    visibility: visible;
}

.search-overlay-content {
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    border-radius: 20px;
    padding: 40px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.5);
    transform: scale(0.8) translateY(-50px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.search-overlay.active .search-overlay-content {
    transform: scale(1) translateY(0);
}

/* (continued full Search Overlay CSS...) */