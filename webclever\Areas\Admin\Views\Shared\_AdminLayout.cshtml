@{
    // L<PERSON>y tên controller hiện tại một cách an toàn
    string currentController = ViewContext.RouteData.Values["controller"]?.ToString() ?? string.Empty;
}
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@(ViewData["Title"] != null ? ViewData["Title"] : "WebClever Admin")</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/admin.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/2.2.0/css/dataTables.dataTables.min.css" rel="stylesheet" />
    <link href="https://cdn.datatables.net/2.2.0/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar" class="bg-dark text-white">
            <div class="sidebar-header">
                <h3>WebClever Admin</h3>
            </div>

            <ul class="list-unstyled components">
                <li class="@(currentController == "Home" ? "active" : "")">
                    <a asp-area="Admin" asp-controller="Home" asp-action="Index">
                        <i class="fas fa-home"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Products" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Products" asp-action="Index">
                        <i class="fas fa-box"></i>
                        <span>Sản phẩm</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Categories" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Categories" asp-action="Index">
                        <i class="fas fa-tags"></i>
                        <span>Danh mục</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Orders" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Orders" asp-action="Index">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Đơn hàng</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Users" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Users" asp-action="Index">
                        <i class="fas fa-users"></i>
                        <span>Khách hàng</span>
                    </a>
                </li>
                <li class="@(currentController == "Banners" ? "active" : "")">
                    <a asp-area="Admin" asp-controller="Banners" asp-action="Index">
                        <i class="fas fa-images"></i> Banner
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Ranks" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Ranks" asp-action="Index">
                        <i class="fas fa-star"></i>
                        <span>Hạng khách hàng</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Promotions" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Promotions" asp-action="Index">
                        <i class="fas fa-percent"></i>
                        <span>Khuyến mãi</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Vouchers" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Vouchers" asp-action="Index">
                        <i class="fas fa-ticket-alt"></i>
                        <span>Mã giảm giá</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Reviews" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Reviews" asp-action="Index">
                        <i class="fas fa-comments"></i>
                        <span>Đánh giá</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(currentController == "Wishlists" ? "active" : "")" 
                       asp-area="Admin" asp-controller="Wishlists" asp-action="Index">
                        <i class="fas fa-heart"></i>
                        <span>Yêu thích</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-dark">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="ms-auto">
                        <div class="dropdown">
                            <button class="btn btn-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> Admin
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> Cài đặt</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/Account/Logout"><i class="fas fa-sign-out-alt"></i> Đăng xuất</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <div class="container-fluid">
                @RenderBody()
            </div>
        </div>
    </div>

    <!-- jQuery luôn đầu tiên -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>

    <!-- Bootstrap bundle, SweetAlert2, v.v. -->
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- jQuery-validation (nếu dùng) -->
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>

    <!-- Custom scripts -->
    @await RenderSectionAsync("Scripts", required: false)
    <script src="~/js/admin.js"></script>

    <script>
        $(document).ready(function () {
            // Kiểm tra và hiển thị thông báo lỗi
            if ('@TempData["Error"]' !== '') {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: '@TempData["Error"]'
                });
            }

            // Kiểm tra và hiển thị thông báo thành công
            if ('@TempData["Success"]' !== '') {
                Swal.fire({
                    icon: 'success',
                    title: 'Thành công!',
                    text: '@TempData["Success"]',
                    showConfirmButton: false,
                    timer: 1500
                });
            }
        });
    </script>
</body>
</html> 