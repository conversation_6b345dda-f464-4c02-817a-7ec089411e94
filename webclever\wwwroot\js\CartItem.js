﻿document.addEventListener("DOMContentLoaded", function () {
    const cartRows = document.querySelectorAll(".cartitem-row");

    cartRows.forEach(row => {
        const minusBtn = row.querySelector(".cartitem-btn:first-child");
        const plusBtn = row.querySelector(".cartitem-btn:last-child");
        const qtyInput = row.querySelector(".cartitem-qty-input");
        const unitPriceElem = row.querySelector(".cartitem-price");
        const lineTotalElem = row.querySelector(".cartitem-total");

        const getPrice = (text) => {
            return parseInt(text.replace(/[₫,.]/g, '').trim()) || 0;
        };

        const formatPrice = (num) => {
            return num.toLocaleString('vi-VN') + '₫';
        };

        const updateTotal = () => {
            const qty = parseInt(qtyInput.value) || 1;
            const price = getPrice(unitPriceElem.innerText);
            const newTotal = qty * price;
            lineTotalElem.innerText = formatPrice(newTotal);

            updateCartTotal();
        };

        plusBtn.addEventListener("click", function () {
            let qty = parseInt(qtyInput.value) || 1;
            qtyInput.value = qty + 1;
            updateTotal();
        });

        minusBtn.addEventListener("click", function () {
            let qty = parseInt(qtyInput.value) || 1;
            if (qty > 1) {
                qtyInput.value = qty - 1;
                updateTotal();
            }
        });
    });

    function updateCartTotal() {
        const lineTotals = document.querySelectorAll(".cartitem-total");
        let total = 0;

        lineTotals.forEach(elem => {
            total += parseInt(elem.innerText.replace(/[₫,.]/g, '').trim()) || 0;
        });

        const totalAmountElem = document.querySelector(".total-amount");
        if (totalAmountElem) {
            totalAmountElem.innerText = total.toLocaleString('vi-VN') + '₫';
        }
    }
});
