﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webclever.Models;

/// <summary>
/// Model đại diện cho hạng khách hàng trong hệ thống
/// </summary>
public partial class CustomerRank
{
    [Key]
    public int RankId { get; set; }

    [Required(ErrorMessage = "Tên hạng không được để trống")]
    [StringLength(50, ErrorMessage = "Tên hạng không được vượt quá 50 ký tự")]
    [Display(Name = "Tên hạng")]
    public string? RankName { get; set; }

    [Required(ErrorMessage = "Chi tiêu tối thiểu không được để trống")]
    [Range(0, double.MaxValue, ErrorMessage = "Chi tiêu tối thiểu phải lớn hơn 0")]
    [Display(Name = "Chi tiêu tối thiểu")]
    [Column(TypeName = "decimal(10,2)")]
    public decimal? MinTotalSpend { get; set; }

    [Required(ErrorMessage = "Tỷ lệ giảm giá không được để trống")]
    [Range(0, 100, ErrorMessage = "Tỷ lệ giảm giá phải từ 0 đến 100")]
    [Display(Name = "Tỷ lệ giảm giá (%)")]
    [Column(TypeName = "decimal(4,2)")]
    public decimal? DiscountRate { get; set; }

    // Navigation property
    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
