﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webclever.Models;

public partial class Category
{
    [Key]
    public int CategoryId { get; set; }

    [Required(ErrorMessage = "<PERSON><PERSON> lòng nhập tên danh mục")]
    [StringLength(100, ErrorMessage = "Tên danh mục không được vượt quá 100 ký tự")]
    [Display(Name = "Tên danh mục")]
    public string? Name { get; set; }

    [StringLength(255, ErrorMessage = "Mô tả không được vượt quá 255 ký tự")]
    [Display(Name = "Mô tả")]
    public string? Description { get; set; }

    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}
